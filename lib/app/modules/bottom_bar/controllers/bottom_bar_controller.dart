
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:incenti_ai/app/modules/communities/controllers/communities_controller.dart';
import 'package:incenti_ai/app/modules/communities/views/communities_view.dart';
import 'package:incenti_ai/app/modules/explore/controllers/explore_controller.dart';
import 'package:incenti_ai/app/modules/explore/views/explore_view.dart';
import 'package:incenti_ai/app/modules/projects/controllers/projects_controller.dart';
import 'package:incenti_ai/app/modules/todo_view/views/todo_view.dart';
import 'package:incenti_ai/utillites/current_user.dart';
import 'package:restart_app/restart_app.dart';

import '../../../../constants/app_image.dart';
import '../../../../main.dart';
import '../../../../services/app_link_service.dart';
import '../../../../utillites/common_function.dart';
import '../../../../utillites/theme_controller.dart';
import '../../projects/views/projects_view.dart';
import '../../setting/views/setting_view.dart';
import '../../todo_view/controllers/todo_view_controller.dart';

class BottomBarController extends GetxController {
  var args = Get.arguments;
  ScrollController scrollController = ScrollController();

  @override
  Future<void> onReady() async {
    super.onReady();
    AppLinkService().init();
    await CommonFunction.getFcmToken();
    _checkThemeReminder();
  }

  void _checkThemeReminder() async {
    final themeReminder = box.read('themeReminder');
    final themeReminderShown = box.read('themeReminderShown') ?? false;
    if (themeReminder != null && !themeReminderShown) {
      final reminderTime = DateTime.tryParse(themeReminder.toString());
      if (reminderTime != null) {
        final now = DateTime.now();
        if (now.difference(reminderTime).inHours >= 24) {
          await Future.delayed(Duration(milliseconds: 800));
          showThemeDialog(context: Get.context!,onConfirm: (){
            Get.back();
            box.write('themeReminderShown', true);
            Restart.restartApp();
            Get.find<ThemeController>().changeTheme();
          },onCancel: (){
            box.write('themeReminderShown', true);
            Get.back();
          },title: "Want to change theme?",message: "Do you want to switch the app theme to light Mode? You can change it back anytime in settings.");
        }
      }
    }
  }

  @override
  Future<void> onInit() async {
    // TODO: implement onInit
    super.onInit();
    if(CurrentUser.user.customSetting?.homeSection == "projects") {
      Get.put(ProjectsController());
      currentIndex.value = 1;
    } else if(CurrentUser.user.customSetting?.homeSection == "communities") {
      Get.put(CommunitiesController());
      currentIndex.value = 3;
    } else if(CurrentUser.user.customSetting?.homeSection == "to dos") {
      Get.put(TodoViewController());
      currentIndex.value = 4;
    } else if(CurrentUser.user.customSetting?.homeSection == "profile") {
      currentIndex.value = 0;
    } else {
      currentIndex.value = 0;
    }
    if(args != null && args["index"] != null) {
      currentIndex.value = args["index"];
      if(currentIndex.value == 4) {
        Get.put(TodoViewController());
      }
    }
    Get.put(ProjectsController());
    Get.put(ExploreController());
    if(currentIndex.value == 1) {
      Get.put(ProjectsController());
    }
    if (args != null && args['isFirstTime'] != null) {
      Future.delayed(
        Duration(milliseconds: 500),
        () {
          CommonFunction.showCustomSnackbar(
            message:
                "Congratulations! Your account has been successfully created.",
          );
        },
      );
    }
  }





  RxInt currentIndex = 0.obs;
  RxList pages = [
    const ExploreView(),
    // const ExploreJobsView(),
    const ProjectsView(),
    SizedBox(),
    const CommunitiesView(),
    const TodosView(),
  ].obs;

  RxList iconData = [
    {"iconPath": AppImage.postIcon, "label": "Write"},
    {"iconPath": AppImage.storyIcon, "label": "Story"},
    {"iconPath": AppImage.todosIcon, "label": "To Do"},
    {"iconPath": AppImage.projectIcon, "label": "Project"},
  ].obs;

  RxList titles = [
    "Feed",
    "Projects",
    "Create",
    "Communities",
    "To Do",
  ].obs;

  RxList images = [
    AppImage.navExplore,
    AppImage.navProjects,
    AppImage.createIcon,
    AppImage.navCommunication,
    AppImage.navTodos,
  ].obs;

  RxList deActiveImages = [
    AppImage.navDcExplore,
    AppImage.navDcProject,
    AppImage.createIcon,
    AppImage.navDcCommunication,
    AppImage.navDcTodos,
  ].obs;

  RxList floatingIcons = [
    AppImage.editFloating,
    AppImage.projectFloating,
    AppImage.navHelp,
    AppImage.navCommunication,
    AppImage.navTodos,
  ].obs;
}
