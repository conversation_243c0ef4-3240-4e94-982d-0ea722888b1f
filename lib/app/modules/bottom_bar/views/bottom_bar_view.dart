import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:incenti_ai/app/modules/communities/controllers/communities_controller.dart';
import 'package:incenti_ai/app/modules/create_todo_view/controllers/create_todo_view_controller.dart';
import 'package:incenti_ai/app/modules/explore/controllers/explore_controller.dart';
import '../../../../constants/app_image.dart';
import '../../../../constants/app_size_constant.dart';
import '../../../../main.dart';
import '../../../../utillites/app_theme.dart';
import '../../../../utillites/typography.dart';
import '../../../routes/app_pages.dart';
import '../../chat_bot_view/views/chat_bot_view.dart';
import '../../create_todo_view/views/create_todo_view.dart';
import '../../explore/components/custom_icon_picker.dart';
import '../../projects/controllers/projects_controller.dart';
import '../../todo_view/controllers/todo_view_controller.dart';
import '../../user_detail/components/image_picker_bottom_sheet.dart';
import '../controllers/bottom_bar_controller.dart';

class BottomBarView extends GetWidget<BottomBarController> {
  const BottomBarView({super.key});

  @override
  Widget build(BuildContext context) {
    MySize().init(context);
    return SafeArea(
      top: false,
      bottom: Platform.isAndroid,
      child: Scaffold(
        resizeToAvoidBottomInset: false,
        // backgroundColor: AppTheme.white,
        body: Obx(() => controller.pages[controller.currentIndex.value]),
        bottomNavigationBar: customBottomNavigation(),
        floatingActionButton: Padding(
          padding: EdgeInsets.only(right: MySize.size8 ?? 8),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              SizedBox(
                width: MySize.size60,
                height: MySize.size60,
                child: Obx(
                  () => FloatingActionButton(
                    heroTag: 'action1',
                    onPressed: () {
                      HapticFeedback.lightImpact();
                      if (controller.currentIndex.value == 0) {
                        ImagePickerBottomSheet.show(
                          context: context,
                          child: CustomIconPicker(
                            iconData: controller.iconData,
                            title: "What do you",
                            subtitle1: "want to",
                            subtitle2: " create?",
                          ),
                        );
                      } else if (controller.currentIndex.value == 1) {
                        Get.toNamed(Routes.create_project)?.then(
                          (value) {
                            Get.find<ProjectsController>().page.value = 1;
                            Get.find<ProjectsController>().hasMoreData.value =
                                true;
                            Get.find<ProjectsController>().createdProject.clear();
                            Get.find<ProjectsController>()
                                .followedProject
                                .clear();
                            Get.find<ProjectsController>().exploreProject.clear();
                            Get.find<ProjectsController>().callApiForProjectData(
                                context: context,
                                isSelected: Get.find<ProjectsController>()
                                            .selectedTabIndex
                                            .value !=
                                        0
                                    ? false
                                    : true,
                                isFollowed: Get.find<ProjectsController>()
                                        .selectedTabIndex
                                        .value ==
                                    1);
                          },
                        );
                      } else if (controller.currentIndex.value == 3) {
                        Get.toNamed(Routes.create_community)?.then((value) {
                          Get.find<CommunitiesController>().page.value = 1;
                          Get.find<CommunitiesController>().hasMoreData.value =
                              true;
                          Get.find<CommunitiesController>()
                              .communityCreatedList
                              .clear();
                          Get.find<CommunitiesController>()
                              .communityJoinedList
                              .clear();
                          Get.find<CommunitiesController>()
                              .communityExploreList
                              .clear();
                          Get.find<CommunitiesController>()
                              .callApiForGetCommunity(context: Get.context!);
                        });
                      } else if (controller.currentIndex.value == 4) {
                        Get.bottomSheet(
                          const CreateTodoView(),
                          isScrollControlled: true,
                          // Allows full-screen height if needed
                          backgroundColor: Colors.white,
                          // Ensures background consistency
                          shape: RoundedRectangleBorder(
                            borderRadius:
                                BorderRadius.vertical(top: Radius.circular(20)),
                          ),
                        ).then(
                          (value) {
                            Get.find<TodoViewController>().page.value = 1;
                            Get.find<TodoViewController>().hasMoreData.value =
                                true;
                            Get.find<TodoViewController>().todoList.clear();
                            Get.find<TodoViewController>()
                                .callApiForToDo(context: Get.context!);
                            Get.delete<CreateTodoViewController>();
                          },
                        );
                      }
                    },
                    backgroundColor: AppTheme.primary1,
                    shape: const CircleBorder(),
                    child: Padding(
                      padding: EdgeInsets.all(MySize.size17 ?? 17),
                      child: SvgPicture.asset(
                        controller.floatingIcons[controller.currentIndex.value],
                        width: MySize.size26,
                        height: MySize.size26,
                        color: AppTheme.white,
                      ),
                    ),
                  ),
                ),
              ),
              Space.height(15),
              SizedBox(
                width: MySize.size60,
                height: MySize.size60,
                child: FloatingActionButton(
                  heroTag: 'action2',
                  onPressed: () {
                    HapticFeedback.lightImpact();
                    Get.bottomSheet(
                      const ChatBotView(),
                      isScrollControlled: true,
                      backgroundColor: Colors.white,
                      shape: RoundedRectangleBorder(
                        borderRadius:
                            BorderRadius.vertical(top: Radius.circular(20)),
                      ),
                    );
                  },
                  backgroundColor: Color(0xFFe8ab2e),
                  shape: const CircleBorder(),
                  child: SvgPicture.asset(
                    AppImage.chatBot,
                  ),
                ),
              ),
              Space.height(10),
            ],
          ),
        ),
      ),
    );
  }
}

Widget customBottomNavigation() {
  return Container(
    height: Platform.isAndroid ? MySize.size70 : MySize.size85,
    width: double.infinity,
    padding:
        EdgeInsets.only(bottom: Platform.isAndroid ? 0 : MySize.size10 ?? 10),
    decoration: BoxDecoration(
      color: AppTheme.getBottomBackgroundColor(Get.context!),
      border: box.read('isDarkMode')
          ? BorderDirectional(top: BorderSide(color: AppTheme.borderColor))
          : null,
      boxShadow: [
        BoxShadow(
          color: AppTheme.getBottomNavigationBarColor(Get.context!),
          spreadRadius: 0,
          blurRadius: 6,
          offset: const Offset(5.0, 5.0),
        )
      ],
    ),
    child: ClipRRect(
      borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(10.0), topRight: Radius.circular(10.0)),
      child: Obx(
        () => Row(
          children: [
            myIcon(0),
            myIcon(1),
            myIcon(2),
            myIcon(3),
            myIcon(4),
          ],
        ),
      ),
    ),
  );
}

Widget myIcon(int index) {
  final controller = Get.find<BottomBarController>();
  return Expanded(
    child: InkWell(
      onTap: () {
        HapticFeedback.lightImpact();
        if ([
              Routes.project_detail,
              Routes.community_detail,
              Routes.profile,
              Routes.other_user_profile,
              Routes.subProject_detail
            ].contains(Get.currentRoute) &&
            index != 2) {
          // Get.back();
          Get.offNamedUntil(
            Routes.Bottom_Bar,
            (route) => route.settings.name == Routes.Bottom_Bar,
          );
        }

        if (index == 2) {
          ImagePickerBottomSheet.show(
            context: Get.context!,
            child: CustomIconPicker(
              iconData: controller.iconData,
              title: "What do you",
              subtitle1: "want to",
              subtitle2: " create?",
            ),
          );
          return;
        }
        controller.currentIndex.value = index;
        if (controller.currentIndex.value == 0) {
          Get.put(ExploreController());
          WidgetsBinding.instance.addPostFrameCallback((_) async {
            final currentOffset = controller.scrollController.offset;
            final duration = Duration(
              milliseconds: (currentOffset * 0.5)
                  .clamp(300, 2000)
                  .toInt(), // tweak these values
            );
            controller.scrollController.animateTo(
              0,
              duration: duration,
              curve: Curves.easeInOut,
            );
          });

        } else if (controller.currentIndex.value == 1) {
          Get.find<ProjectsController>().page.value = 1;
          Get.find<ProjectsController>().hasMoreData.value = true;
          Get.find<ProjectsController>().createdProject.clear();
          Get.find<ProjectsController>().followedProject.clear();
          Get.find<ProjectsController>().exploreProject.clear();
          Get.find<ProjectsController>().callApiForProjectData(
              context: Get.context!,
              isSelected:
                  Get.find<ProjectsController>().selectedTabIndex.value == 0,
              isFollowed:
                  Get.find<ProjectsController>().selectedTabIndex.value == 1,
                    );
        } else if (controller.currentIndex.value == 3) {
          Get.put(CommunitiesController());
        } else if (controller.currentIndex.value == 4) {
          Get.put(TodoViewController());
        }
      },
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Space.height(12),
          SvgPicture.asset(
            controller.currentIndex.value == index
                ? controller.deActiveImages[index]
                : controller.images[index],
            height: MySize.size19,
            width: MySize.size22,
            color: box.read('isDarkMode')
                ? (controller.currentIndex.value == index || index == 2)
                    ? AppTheme.white[50]
                    : AppTheme.white.withValues(alpha: 0.3)
                : (controller.currentIndex.value == index || index == 2)
                    ? AppTheme.primary1
                    : AppTheme.baseBlack,
          ),
          Padding(
            padding: EdgeInsets.only(
              top: MySize.getScaledSizeHeight(8),
            ),
            child: FittedBox(
              child: TypoGraphy(
                text: controller.titles[index],
                level: 2,
                fontWeight: FontWeight.w500,
                color: box.read('isDarkMode')
                    ? AppTheme.white
                    : (controller.currentIndex.value == index || index == 2)
                        ? AppTheme.primary1
                        : AppTheme.baseBlack,
              ),
            ),
          ),
          Space.height(10),
        ],
      ),
    ),
  );
}
