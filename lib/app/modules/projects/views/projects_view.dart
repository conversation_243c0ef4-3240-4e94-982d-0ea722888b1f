import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:incenti_ai/app/modules/bottom_bar/controllers/bottom_bar_controller.dart';
import 'package:incenti_ai/utillites/current_user.dart';
import '../../../../constants/app_image.dart';
import '../../../../constants/app_size_constant.dart';
import '../../../../utillites/app_text_field.dart';
import '../../../../utillites/app_theme.dart';
import '../../../../utillites/common_profile_widget.dart';
import '../../../../utillites/common_report_bottom_sheet.dart';
import '../../../../utillites/common_shimmer_grid_view.dart';
import '../../../../utillites/typography.dart';
import '../../../routes/app_pages.dart';
import '../../explore/components/common_widget_view.dart';
import '../../profile_view/components/common_project_view.dart';
import '../../user_detail/components/image_picker_bottom_sheet.dart';
import '../controllers/projects_controller.dart';

class ProjectsView extends StatefulWidget {
  const ProjectsView({super.key});

  @override
  State<ProjectsView> createState() => _ProjectsViewState();
}

class _ProjectsViewState extends State<ProjectsView>
    with TickerProviderStateMixin {
  final ProjectsController controller = Get.put(ProjectsController());

  @override
  Widget build(BuildContext context) {
    print("size == ${MediaQuery.of(context).size.width}");
    return Scaffold(
      resizeToAvoidBottomInset: false,
      // backgroundColor: AppTheme.white,
      body: Column(
        children: [
          Space.height(40),
          SizedBox(
            height: MySize.getScaledSizeHeight(65),
            child: Padding(
              padding: EdgeInsets.symmetric(
                  horizontal: MySize.getScaledSizeWidth(20) ?? 30),
              child: Row(
                children: [
                  InkWell(
                    onTap: () {
                      Get.find<BottomBarController>().currentIndex.value = 0;
                    },
                    child: Container(
                      // color: Colors.red,
                      alignment: Alignment.center, width: MySize.size40,
                      child: Image.asset(
                        AppImage.appShortIcon,
                        color: AppTheme.whiteWithBase,
                        height: MySize.size31,
                      ),
                    ),
                  ),
                  Spacer(),
                  Obx(
                    () => Row(
                      children: List.generate(
                        3,
                        (index) {
                          bool isDisabled =
                              controller.previousTabIndex.value == index;
                          return Padding(
                            padding: EdgeInsets.only(
                                left: MySize.size10 ?? 20,
                                right: MySize.size10 ?? 20),
                            child: InkWell(
                              highlightColor: Colors.transparent,
                              splashColor: Colors.transparent,
                              onTap: isDisabled
                                  ? null
                                  : () {
                                      if (controller.selectedTabIndex.value ==
                                          index) return;

                                      controller.previousTabIndex.value = index;
                                      controller.selectedTabIndex.value = index;
                                      controller.page.value = 1;
                                      controller.hasMoreData.value = true;

                                      // Show loader only for the selected tab
                                      if (index == 0) {
                                        controller.isLoadingCreated.value =
                                            true;
                                      } else if (index == 1) {
                                        controller.isLoadingFollowed.value =
                                            true;
                                      } else {
                                        controller.isLoadingExplore.value =
                                            true;
                                      }
                                      controller.followedProject.clear();
                                      controller.createdProject.clear();
                                      controller.exploreProject.clear();
                                      if (index == 0) {
                                        controller.callApiForProjectData(
                                            context: context, isSelected: true);
                                      } else if (index == 1) {
                                        controller.callApiForProjectData(
                                            context: context, isFollowed: true);
                                      } else {
                                        controller.callApiForProjectData(
                                            context: context);
                                      }
                                    },
                              child: TypoGraphy(
                                text: controller.tabList[index],
                                level: MediaQuery.of(context).size.width > 350 ? 12 : 3,
                                fontWeight:
                                    controller.selectedTabIndex.value == index
                                        ? FontWeight.w700
                                        : FontWeight.w300,
                              ),
                            ),
                          );
                        },
                      ),
                    ),
                  ),
                  Spacer(),
                  InkWell(
                    onTap: () {
                      Get.toNamed(Routes.profile,arguments: {"isNeedBottom": true});
                      CurrentUser.getMe(callback: () async {});
                    },
                    child: Obx(
                      () => profileImage(
                          url: CurrentUser.user.image ?? "",
                          width: MySize.size34 ?? 25,
                          height: MySize.size34 ?? 25,
                          iconHeight: MySize.size34 ?? 25,
                          iconWidth: MySize.size34 ?? 25,
                          borderColor: Colors.transparent,
                          color: AppTheme.darkGrey[100]),
                    ),
                  ),
                  Space.width(10),
                ],
              ),
            ),
          ),
          Padding(
            padding: EdgeInsets.symmetric(horizontal: MySize.size30 ?? 30),
            child: ValueListenableBuilder(
              valueListenable: controller.searchController.value,
              builder: (context, value, child) {
                return AppTextField(
                  controller: controller.searchController.value,
                  focusNode: controller.searchFocusNode,
                  padding: EdgeInsets.only(
                    top: MySize.size12 ?? 12,
                    bottom: MySize.size10 ?? 12,
                  ),
                  onChangedValue: (text) {
                    if ((text?.trim() ?? "").isNotEmpty) {
                      controller.page.value = 1;
                      controller.hasMoreData.value = true;
                      controller.callApiForProjectData(
                        context: context,
                        isSelected: controller.selectedTabIndex.value == 0,
                        isFollowed: controller.selectedTabIndex.value == 1,
                      );
                    }
                  },
                  height: MySize.getScaledSizeHeight(50),
                  prefixIcon: Padding(
                    padding: EdgeInsets.all(4),
                    child: SvgPicture.asset(
                      AppImage.searchIcon,
                      height: MySize.size24,
                      width: MySize.size24,
                      color: AppTheme.grey,
                    ),
                  ),
                  suffixIcon:
                      (controller.searchController.value.text.isNotEmpty)
                          ? GestureDetector(
                              onTap: () {
                                controller.searchController.value.clear();
                                controller.page.value = 1;
                                Future.delayed(
                                  Duration(milliseconds: 500),
                                  () {
                                    controller.exploreProject.clear();
                                    controller.followedProject.clear();
                                    controller.createdProject.clear();
                                    controller.pullRefresh();
                                  },
                                );
                              },
                              child: Padding(
                                padding:
                                    EdgeInsets.only(right: MySize.size15 ?? 20),
                                child: SvgPicture.asset(
                                  AppImage.textFieldClear,
                                  // height: MySize.size20,
                                  // width: MySize.size20,
                                  color: AppTheme.grey,
                                ),
                              ),
                            )
                          : null,
                  hintText: "Search",
                  hintStyle: TextStyle(
                    color: AppTheme.grey,
                    fontSize: MySize.size16,
                    fontWeight: FontWeight.w400,
                  ),
                );
              },
            ),
          ),
          Space.height(30),
          Obx(() {
            if (controller.selectedTabIndex.value == 0 &&
                controller.isLoadingCreated.value &&
                controller.createdProject.isEmpty) {
              return Expanded(
                  child: Padding(
                padding: EdgeInsets.symmetric(horizontal: MySize.size30 ?? 30),
                child: ShimmerGridView(),
              ));
            }
            if (controller.selectedTabIndex.value == 1 &&
                controller.isLoadingFollowed.value &&
                controller.followedProject.isEmpty) {
              return Expanded(
                  child: Padding(
                padding: EdgeInsets.symmetric(horizontal: MySize.size30 ?? 30),
                child: ShimmerGridView(),
              ));
            }
            if (controller.selectedTabIndex.value == 2 &&
                controller.isLoadingExplore.value &&
                controller.exploreProject.isEmpty) {
              return Expanded(
                  child: Padding(
                padding: EdgeInsets.symmetric(horizontal: MySize.size30 ?? 30),
                child: ShimmerGridView(),
              ));
            }
            return Expanded(
              child: RefreshIndicator(
                onRefresh: () async {
                  controller.pullRefresh();
                },
                child: Padding(
                  padding: EdgeInsets.symmetric(
                      horizontal: MySize.getScaledSizeWidth(30)),
                  child: CommonProjectView(
                    isUser: controller.selectedTabIndex.value != 2,
                    isSearchWidget:
                        controller.searchController.value.text.isNotEmpty &&
                            controller.selectedProjectList.isEmpty,
                    isIllustratorVisible:
                        controller.searchController.value.text.isNotEmpty &&
                            controller.selectedProjectList.isEmpty,
                    isFollowedVisible: controller.selectedTabIndex.value == 1,
                    controller: controller,
                    listData: controller.selectedProjectList,
                    callBack: controller.selectedTabIndex.value != 2 ? () {
                      controller.page.value = 1;
                      controller.hasMoreData.value = true;

                      // Show loader only for the selected tab
                      if (controller.selectedTabIndex.value == 0) {
                        controller.isLoadingCreated.value = true;
                      } else if (controller.selectedTabIndex.value == 1) {
                        controller.isLoadingFollowed.value = true;
                      } else {
                        controller.isLoadingExplore.value = true;
                      }
                      controller.followedProject.clear();
                      controller.createdProject.clear();
                      controller.exploreProject.clear();
                      if (controller.selectedTabIndex.value == 0) {
                        controller.callApiForProjectData(
                            context: context, isSelected: true);
                      } else if (controller.selectedTabIndex.value == 1) {
                        controller.callApiForProjectData(
                            context: context, isFollowed: true);
                      } else {
                        controller.callApiForProjectData(context: context);
                      }
                      // controller.callApiForProjectData(
                      //     context: context,
                      //     isSelected: controller.selectedTabIndex.value == 0,
                      //     isFollowed: controller.selectedTabIndex.value == 1);
                    } : null,
                    onTap: (index) {
                      if (controller.searchFocusNode.hasFocus) {
                        controller.searchFocusNode.unfocus();
                      }
                      ImagePickerBottomSheet.show(
                        context: context,
                        child: controller.selectedTabIndex.value != 0
                            ? showPostOption(
                                isProject: true,
                          projectId: controller.selectedTabIndex.value == 1
                              ? controller.followedProject[index].id
                              : controller.exploreProject[index].id,
                          projectSlug:
                          controller.selectedTabIndex.value == 1
                              ? controller.followedProject[index].slug
                              : controller.exploreProject[index].slug,
                          title: controller.selectedTabIndex.value == 1
                              ? controller.followedProject[index].name
                              : controller.exploreProject[index].name,
                                onTap: () {
                                  Navigator.pop(context);
                                  showCommonReportBottomSheet(
                                    context: context,
                                    title: "Report",
                                    subTitle:
                                        "Why are you reporting this project?",
                                    description:
                                        "Your report is anonymous. If someone is in\nimmediate danger, call the local emergency\nservices - don't wait.",
                                    options: controller.repostData,
                                    onOptionTap: (selectedOption) async {
                                      controller.selectedReason.value =
                                          selectedOption;
                                      await controller.callApiForReportProject(
                                        context: context,
                                        projectId: controller.selectedProjectList[index]
                                            .id
                                            .toString(),
                                      );
                                    },
                                  );
                                },
                              )
                            : (controller.selectedTabIndex.value == 0 &&
                                    controller.createdProject[index].userId !=
                                        CurrentUser.user.id &&
                                    (controller.createdProject[index]
                                            .projectMembers.isNotEmpty))
                                ? showPostOption(
                                    isProject: true,
                                    projectId:
                                        controller.createdProject[index].id,  projectSlug:
                                        controller.createdProject[index].slug,
                                    title:
                                        controller.createdProject[index].name,
                                    onTap: () {
                                      Navigator.pop(context);
                                      showCommonReportBottomSheet(
                                        context: context,
                                        title: "Report",
                                        subTitle:
                                            "Why are you reporting this project?",
                                        description:
                                            "Your report is anonymous. If someone is in\nimmediate danger, call the local emergency\nservices - don't wait.",
                                        options: controller.repostData,
                                        onOptionTap: (selectedOption) async {
                                          controller.selectedReason.value =
                                              selectedOption;
                                          await controller
                                              .callApiForReportProject(
                                            context: context,
                                            projectId:
                                            controller.selectedProjectList[index]
                                                    .id
                                                    .toString(),
                                          );
                                        },
                                      );
                                    },
                                  )
                                : showPostEditOption(
                                    isNotDelete: (controller
                                            .createdProject[index]
                                            .projectMembers
                                            .isNotEmpty &&
                                        controller.createdProject[index]
                                                .projectMembers[0].access ==
                                            "write"),
                                    projectId: controller
                                        .createdProject[index].id
                                        .toString(),
                                    isPrivate: controller
                                        .createdProject[index].isPrivate,
                                    isHide:
                                        controller.createdProject[index].hide ??
                                            false,
                                    hidePrivate: () {
                                      controller.callApiForHideProject(
                                          context: context,
                                          projectId: controller
                                              .createdProject[index].id
                                              .toString(),
                                          isHide: controller
                                                      .createdProject[index]
                                                      .hide ==
                                                  false
                                              ? true
                                              : false,
                                          index: index);
                                    },
                                    projectName:
                                        controller.createdProject[index].name),
                      );
                    },
                  ),
                ),
              ),
            );
          }),
        ],
      ),
    );
  }
}
