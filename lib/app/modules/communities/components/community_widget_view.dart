import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';

import '../../../../constants/app_image.dart';
import '../../../../constants/app_size_constant.dart';
import '../../../../models/app_community_model.dart';
import '../../../../utillites/app_text_field.dart';
import '../../../../utillites/app_theme.dart';
import '../../../../utillites/common_profile_widget.dart';
import '../../../../utillites/current_user.dart';
import '../../../../utillites/custom_sliver_list_view.dart';
import '../../../../utillites/empty.dart';
import '../../../../utillites/typography.dart';
import '../../../routes/app_pages.dart';
import '../../bottom_bar/controllers/bottom_bar_controller.dart';
import '../controllers/communities_controller.dart';
import 'common_community_caard.dart';
import 'community_caard_shimmer_view.dart';

Widget buildHeader({required CommunitiesController controller,required BuildContext context}) {
  return SizedBox(
    height: MySize.getScaledSizeHeight(65),
    child: Padding(
      padding: EdgeInsets.symmetric(horizontal: MySize.size20 ?? 30),
      child: Row(
        children: [
          InkWell(
            onTap: () {
              Get.find<BottomBarController>().currentIndex.value = 0;
            },
            child: Container(
              alignment: Alignment.center,
              width: MySize.size40,
              child: Image.asset(
                AppImage.appShortIcon,
                color: AppTheme.whiteWithBase,
                height: MySize.size31,
              ),
            ),
          ),
          Spacer(),
          Obx(() => Row(
            children: List.generate(3, (index) {
              return Padding(
                padding: EdgeInsets.symmetric(horizontal: MySize.size10 ?? 20),
                child: InkWell(
                  highlightColor: Colors.transparent,
                  splashColor: Colors.transparent,
                  onTap: () {
                    if (controller.selectedTabIndex.value == index) return;
                    controller.selectedTabIndex.value = index;
                    controller.page.value = 1;
                    controller.hasMoreData.value = true;
                    controller.setTabLoading(index);
                    controller.clearCommunityLists();
                    controller.callApiForGetCommunity(context: context);
                  },
                  child: TypoGraphy(
                    text: controller.tabList[index],
                    level: MediaQuery.of(context).size.width > 350 ? 12 : 3,
                    fontWeight: controller.selectedTabIndex.value == index
                        ? FontWeight.w700
                        : FontWeight.w300,
                  ),
                ),
              );
            }),
          )),
          Spacer(),
          InkWell(
            onTap: () {
              Get.toNamed(Routes.profile, arguments: {"isNeedBottom": true});
              CurrentUser.getMe(callback: () async {});
            },
            child: Obx(() => profileImage(
              url: CurrentUser.user.image ?? "",
              width: MySize.size34 ?? 25,
              height: MySize.size34 ?? 25,
              iconHeight: MySize.size34 ?? 25,
              iconWidth: MySize.size34 ?? 25,
              borderColor: Colors.transparent,
              color: AppTheme.darkGrey[100],
            )),
          ),
          Space.width(10),
        ],
      ),
    ),
  );
}

Widget buildSearchField(BuildContext context,{required CommunitiesController controller}) {
  return Padding(
    padding: EdgeInsets.symmetric(horizontal: MySize.size30 ?? 30),
    child: ValueListenableBuilder(
      valueListenable: controller.searchController.value,
      builder: (context, value, child) {
        return AppTextField(
          controller: controller.searchController.value,
          focusNode: controller.searchFocusNode,
          padding: EdgeInsets.symmetric(
            vertical: MySize.size12 ?? 12,
          ),
          onChangedValue: (text) {
            if ((text?.trim() ?? "").isNotEmpty) {
              controller.page.value = 1;
              controller.hasMoreData.value = true;
              controller.setTabLoading(controller.selectedTabIndex.value);
              controller.callApiForGetCommunity(context: context);
            }
          },
          height: MySize.getScaledSizeHeight(50),
          prefixIcon: Padding(
            padding: EdgeInsets.all(4),
            child: SvgPicture.asset(
              AppImage.searchIcon,
              height: MySize.size24,
              width: MySize.size24,
              color: AppTheme.grey,
            ),
          ),
          suffixIcon: (controller.searchController.value.text.isNotEmpty)
              ? GestureDetector(
            onTap: () {
              controller.searchController.value.clear();
              controller.page.value = 1;
              controller.clearCommunityLists();
              controller.pullRefresh();
            },
            child: Padding(
              padding: EdgeInsets.only(right: MySize.size15 ?? 20),
              child: SvgPicture.asset(
                AppImage.textFieldClear,
                color: AppTheme.grey,
              ),
            ),
          )
              : null,
          hintText: "Search",
          hintStyle: TextStyle(
            color: AppTheme.grey,
            fontSize: MySize.size16,
            fontWeight: FontWeight.w400,
          ),
        );
      },
    ),
  );
}

Widget buildCommunityList(BuildContext context,{required CommunitiesController controller}) {
  return Expanded(
    child: RefreshIndicator(
      onRefresh: () async => controller.pullRefresh(),
      child: CustomScrollView(
        physics: AlwaysScrollableScrollPhysics(),
        controller: controller.scrollController,
        keyboardDismissBehavior: ScrollViewKeyboardDismissBehavior.onDrag,
        slivers: [
          Obx(() {
            final list = controller.selectedCommunityList;
            final loading = controller.selectedCommunityLoading;

            if (loading && list.isEmpty) {
              return SliverToBoxAdapter(
                child: ListView.builder(
                  padding: EdgeInsets.zero,
                  shrinkWrap: true,
                  physics: NeverScrollableScrollPhysics(),
                  itemCount: 5,
                  itemBuilder: (context, index) => ShimmerGroupCard(),
                ),
              );
            } else if (controller.searchController.value.text.isNotEmpty && list.isEmpty) {
              return SliverToBoxAdapter(
                child: Padding(
                  padding: EdgeInsets.only(top: MySize.getScaledSizeHeight(250)),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      SvgPicture.asset("assets/images/icon_search.svg", height: MySize.size50, color: AppTheme.whiteWithNull),
                      Empty(title: "Search Result Not Found !"),
                    ],
                  ),
                ),
              );
            } else {
              return CustomSliverListView(
                emptyWidget: Padding(
                  padding: EdgeInsets.symmetric(horizontal: MySize.getScaledSizeHeight(48), vertical: MySize.getScaledSizeHeight(100)),
                  child: Column(
                    children: [
                      SvgPicture.asset(AppImage.noCommunity),
                      Space.height(30),
                      TypoGraphy(
                        text: controller.selectedTabIndex.value == 1
                            ? "No communities joined yet."
                            : controller.selectedTabIndex.value == 2
                            ? "No communities to explore."
                            : "No communities created yet.",
                        level: 12,
                      ),
                    ],
                  ),
                ),
                maximumReachedWidget: const SizedBox(),
                itemBuilder: (context, CommunityData res, index) {
                  return Obx(() => InkWell(
                    onTap: () {
                      Get.toNamed(Routes.community_detail, arguments: {
                        "communityId": res.id,
                        "index": index,
                        "isNeedBottom": true
                      })?.then((value) {
                        if (controller.selectedTabIndex.value != 2) {
                          controller.pullRefresh();
                        }
                      });
                    },
                    child: CommonGroupCard(
                      isJoined: res.isJoined?.value ?? false,
                      title: res.name ?? "",
                      imageUrl: res.image,
                      membersCount: res.members ?? "0",
                      profileImages: res.communityMembers,
                      onJoinPressed: () {
                        HapticFeedback.lightImpact();
                        controller.callApiForJoinCommunity(
                          context: context,
                          communityId: res.id.toString(),
                          index: index,
                        );
                      },
                      selectedTabIndex: controller.selectedTabIndex.value,
                    ),
                  ));
                },
                isLoading: loading,
                items: list,
                hasMoreData: controller.hasMoreData.value,
                onLoadMore: () => controller.callApiForGetCommunity(context: context),
              );
            }
          }),
        ],
      ),
    ),
  );
}