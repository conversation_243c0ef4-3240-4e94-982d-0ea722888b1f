import 'package:flutter/material.dart';
import 'package:incenti_ai/constants/app_image.dart';
import 'package:incenti_ai/utillites/network_image.dart';

import '../../../../constants/app_size_constant.dart';
import '../../../../main.dart';
import '../../../../models/app_member_model.dart';
import '../../../../utillites/app_theme.dart';
import '../../../../utillites/common_profile_widget.dart';
import '../../../../utillites/typography.dart';

class CommonGroupCard extends StatelessWidget {
  final String title;
  final String imageUrl;
  final bool isJoined;
  final String membersCount;
  final int selectedTabIndex;
  final List<MemberData> profileImages;
  final VoidCallback onJoinPressed;

  const CommonGroupCard({
    super.key,
    required this.title,
    required this.imageUrl,
    required this.membersCount,
    required this.isJoined,
    required this.profileImages,
    required this.onJoinPressed,
    required this.selectedTabIndex,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(
          left: MySize.getScaledSizeWidth(30),
          right: MySize.getScaledSizeWidth(30),
          bottom: MySize.getScaledSizeWidth(30)),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          CircleAvatar(
            radius: MySize.getScaledSizeHeight(55),
            backgroundColor: box.read('isDarkMode') ? AppTheme.baseBlack : AppTheme.white,
            child: ClipOval(
              child: imageUrl.isNotEmpty
                  ? NetworkImageComponent(
                      imageUrl: imageUrl,
                      height: MySize.getScaledSizeHeight(108.31),
                      width: MySize.getScaledSizeWidth(110),
                    )
                  : Stack(
                    children: [
                      Image.asset(
                          AppImage.defaultCommunity,
                          height: MySize.getScaledSizeHeight(108.31),
                          width: MySize.getScaledSizeWidth(110),
                          fit: BoxFit.cover,
                        ),
                      Container(
                        height: MySize.getScaledSizeHeight(108.31),
                        width: MySize.getScaledSizeWidth(110),
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            colors: [
                              AppTheme.black.withOpacity(0.1),
                              AppTheme.black.withOpacity(0.1),
                            ],
                            begin: Alignment.topCenter,
                            end: Alignment.bottomCenter,
                          ),
                        ),
                      ),
                    ],
                  ),
            ),
          ),
          Space.width(10),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                TypoGraphy(
                  text: title,
                  level: 11,
                  fontWeight: FontWeight.w700,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                  // color: AppTheme.baseBlack,
                ),
                Space.height(8),
                Row(
                  children: [
                    if (membersCount != "0")
                      SizedBox(
                        width: (profileImages.length > 3
                                    ? 2
                                    : profileImages.length - 1) *
                                MySize.getScaledSizeWidth(11) +
                            MySize.getScaledSizeWidth(20),
                        height: MySize.getScaledSizeHeight(20),
                        child: Stack(
                          clipBehavior: Clip.none,
                          children: List.generate(
                            profileImages.length > 3 ? 3 : profileImages.length,
                            (index) {
                              return Positioned(
                                left: index * MySize.getScaledSizeWidth(11),
                                child: Container(
                                  height: MySize.getScaledSizeHeight(21),
                                  width: MySize.getScaledSizeWidth(21),
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(500),
                                    border: Border.all(
                                        color: AppTheme.white, width: 1),
                                  ),
                                  child: profileImage(
                                      url: profileImages[index].user?.image ??
                                          "",
                                      userName: profileImages[index]
                                              .user
                                              ?.firstName ??
                                          "",
                                      height: MySize.getScaledSizeHeight(40),
                                      width: MySize.getScaledSizeWidth(40),
                                      iconHeight:
                                          MySize.getScaledSizeHeight(40),
                                      iconWidth: MySize.getScaledSizeHeight(40),
                                      borderColor: Colors.transparent,
                                      color: AppTheme.darkGrey[100],
                                      textStyle: TextStyle(
                                          fontSize:
                                              MySize.getScaledSizeHeight(10),
                                          color: AppTheme.white)),
                                ),
                              );
                            },
                          ),
                        ),
                      ),
                    if (membersCount != "0") Space.width(12),
                    Expanded(
                      child: TypoGraphy(
                        text: (int.tryParse(membersCount)! >= 2)
                            ? "$membersCount members are following"
                            : "$membersCount member is following",
                        level: 2,
                        fontWeight: FontWeight.w400,
                        color: AppTheme.grey,
                      ),
                    ),
                  ],
                ),
                Space.height(selectedTabIndex == 0 ? 10 : 15),
                if (selectedTabIndex != 0) ...[
                  if (isJoined == true) ...[
                    GestureDetector(
                      onTap: onJoinPressed,
                      child: Container(
                        height: MySize.getScaledSizeHeight(40),
                        width: MySize.getScaledSizeWidth(84),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(50),
                          color: Theme.of(context).brightness == Brightness.dark ? AppTheme.bottomBar : Colors.transparent,
                          border: Border.all(
                            color: AppTheme.grey,
                            width: 1,
                          ),
                        ),
                        alignment: Alignment.center,
                        child: TypoGraphy(
                          text: "Joined",
                          textStyle: TextStyle(
                            fontSize: MySize.getScaledSizeHeight(15),
                            fontWeight: FontWeight.w600,
                            color: AppTheme.grey,
                          ),
                        ),
                      ),
                    ),
                  ],
                  if (isJoined == false) ...[
                    GestureDetector(
                      onTap: onJoinPressed,
                      child: Container(
                        height: MySize.getScaledSizeHeight(40),
                        width: MySize.getScaledSizeWidth(84),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(50),
                          color: Theme.of(context).brightness == Brightness.dark ? AppTheme.bottomBar : Colors.transparent,
                          border: Border.all(
                            color: Theme.of(context).brightness == Brightness.dark ? AppTheme.grey : AppTheme.primary1,
                            width: 1,
                          ),
                        ),
                        alignment: Alignment.center,
                        child: TypoGraphy(
                          text: "Join",
                          textStyle: TextStyle(
                            fontSize: MySize.getScaledSizeHeight(15),
                            fontWeight: FontWeight.w600,
                            color: AppTheme.primaryIconDark,
                          ),
                        ),
                      ),
                    ),
                  ],
                ],
                if (selectedTabIndex == 0) ...[
                  TypoGraphy(
                    text: "Created by me",
                    level: 2,
                    fontWeight: FontWeight.w400,
                    color: AppTheme.grey,
                    fontStyle: FontStyle.italic,
                  ),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }
}
