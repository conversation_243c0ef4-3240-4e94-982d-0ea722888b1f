import 'dart:developer';

import 'package:dotted_border/dotted_border.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:incenti_ai/constants/app_image.dart';
import 'package:intl/intl.dart';
import '../../../../constants/app_size_constant.dart';
import '../../../../main.dart';
import '../../../../utillites/app_text_field.dart';
import '../../../../utillites/app_theme.dart';
import '../../../../utillites/buttons.dart';
import '../../../../utillites/common_function.dart';
import '../../../../utillites/common_profile_widget.dart';
import '../../../../utillites/loader.dart';
import '../../../../utillites/typography.dart';
import '../components/common_project_dropdown.dart';
import '../components/project_selection_view.dart';
import '../components/sub_project_selection_view.dart';
import '../components/team_member_selection.dart';
import '../controllers/create_todo_view_controller.dart';

class CreateTodoView extends StatelessWidget {
  const CreateTodoView({super.key});

  @override
  Widget build(BuildContext context) {
    MySize().init(context);
    return GetBuilder<CreateTodoViewController>(
      init: CreateTodoViewController(),
      builder: (controller) {
        return Scaffold(
          resizeToAvoidBottomInset: false,
          body: Stack(
            children: [
              Column(
                children: [
                  Space.height(40),
                  Padding(
                    padding: EdgeInsets.only(right: MySize.getScaledSizeWidth(30)),
                    child: Container(
                      padding: EdgeInsets.symmetric(
                          vertical: MySize.getScaledSizeWidth(10)),
                      child: InkWell(
                        onTap: () {
                          Get.back();
                          // if(controller.args != null &&
                          //     controller.args["isBottom"] != null) {
                          //   // Get.back();
                          //   // Get.find<BottomBarController>().currentIndex.value = 4;
                          //   Get.offAllNamed(Routes.Bottom_Bar,
                          //       arguments: {"index": 4});
                          // } else {
                          //   Get.back();
                          // }
                        },
                        child: Align(
                          alignment: Alignment.centerRight,
                          child: SvgPicture.asset(AppImage.closeImage,color: AppTheme.whiteWithBase,),

                        ),
                      ),
                    ),
                  ),
                  Space.height(6),
                  TypoGraphy(
                    text: controller.args != null &&
                        controller.args["isEdit"] != null ? "Edit To Do" : "Create To Do",
                    level: 8,
                    fontWeight: FontWeight.w700,
                  ),
                  Space.height(4),
                  TypoGraphy(
                    text: "List your next To Do item",
                    level: 3,
                    fontWeight: FontWeight.w400,
                    color: AppTheme.grey,
                  ),
                  Expanded(
                    child: SingleChildScrollView(
                      keyboardDismissBehavior:
                          ScrollViewKeyboardDismissBehavior.onDrag,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: [
                          Space.height(30),
                          Padding(
                            padding:
                                EdgeInsets.symmetric(horizontal: MySize.size45 ?? 25),
                            child: AppTextField(
                              focusNode: controller.taskNameFocusNode,
                              controller: controller.taskNameController.value,
                              labelText: "Task Name*",
                              // maxLength: 30,
                              textCapitalization: TextCapitalization.sentences,
                            ),
                          ),
                          ExpansionTile(
                            childrenPadding:
                                EdgeInsets.symmetric(horizontal: MySize.size45 ?? 25),
                            iconColor: AppTheme.whiteWithBase,
                            collapsedIconColor: AppTheme.whiteWithBase,

                            tilePadding:
                                EdgeInsets.symmetric(horizontal: MySize.size45 ?? 25),
                            shape: const RoundedRectangleBorder(
                              side: BorderSide.none,
                            ),
                            title: TypoGraphy(
                              text: "Add More (Optional)",
                              level: 12,
                              fontWeight: FontWeight.w500,
                            ),
                            children: [
                              GetBuilder<CreateTodoViewController>(
                                builder: (controller) => AppTextField(
                                  textInputAction: TextInputAction.newline,
                                  textInputType: TextInputType.multiline,
                                  textCapitalization: TextCapitalization.sentences,
                                  height: MySize.size140,
                                  focusNode:
                                      controller.taskDescriptionFocusNode.value,
                                  controller:
                                      controller.taskDescriptionController.value,
                                  labelText: controller.taskDescriptionFocusNode.value
                                              .hasFocus ||
                                          controller.taskDescriptionController.value
                                              .text.isNotEmpty
                                      ? "Description"
                                      : null,
                                  hintText: controller
                                          .taskDescriptionFocusNode.value.hasFocus
                                      ? ""
                                      : "Description",
                                  maxLines: 5,
                                  maxLength: 250,
                              ),
                  ),
                              Space.height(6),
                              Obx(
                                () => Align(
                                  alignment: Alignment.centerRight,
                                  child: TypoGraphy(
                                    text:
                                        "${controller.charCount}/${controller.maxChars}",
                                    level: 2,
                                    fontWeight: FontWeight.w400,
                                  ),
                                ),
                              ),
                              Space.height(30),
                              Align(
                                alignment: Alignment.centerLeft,
                                child: TypoGraphy(
                                  text: "Select Priority (Optional)",
                                  level: 4,
                                  fontWeight: FontWeight.w400,
                                ),
                              ),
                              Space.height(14),
                              Obx(
                                () => Row(
                                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: controller.priorities.map((priority) {
                                    return ChoiceChip(
                                      padding: EdgeInsets.symmetric(
                                        horizontal: MySize.getScaledSizeWidth(25),
                                        vertical: MySize.getScaledSizeHeight(15),
                                      ),
                                      labelPadding: EdgeInsets.zero,
                                      label: TypoGraphy(
                                        text: priority,
                                        level: 3,
                                        fontWeight: FontWeight.w500,
                                        color: controller.getTextColor(priority),
                                      ),
                                      selected: controller.selectedPriority.value ==
                                          priority,
                                      selectedColor: controller.getColor(priority),
                                      backgroundColor: Theme.of(context).brightness == Brightness.dark ? AppTheme.darkBackground : Colors.transparent,

                                      shape: StadiumBorder(
                                        side: BorderSide(
                                          color: controller.selectedPriority.value ==
                                                  priority
                                              ? controller.getColor(priority)
                                              : AppTheme.grey,
                                        ),
                                      ),
                                      onSelected: (bool selected) {
                                        controller.selectedPriority.value = priority;
                                      },
                                    );
                                  }).toList(),
                                ),
                              ),
                              Space.height(30),
                              AppTextField(
                                onTap: () async {
                                  DateTime? pickedDate = await showDatePicker(
                                    context: context,
                                    initialDate: controller.taskDateController.value
                                        .text.isNotEmpty
                                        ? DateFormat('MMMM dd, yyyy').parse(
                                        controller
                                            .taskDateController.value.text)
                                        : DateTime.now(),
                                    firstDate: ((controller.pickDate ?? DateTime.now()) != null && (controller.pickDate ?? DateTime.now()).isBefore(DateTime.now()))
                                        ? (controller.pickDate ?? DateTime.now())
                                        : DateTime.now(),
                                    lastDate: DateTime(2100),
                                  );

                                  if (pickedDate != null) {
                                    log("pick date == ${DateTime(pickedDate.year, pickedDate.month, pickedDate.day)}");
                                    controller.selectedDate = DateTime(pickedDate.year, pickedDate.month, pickedDate.day);
                                    String formattedDate =
                                    DateFormat('MMMM dd, yyyy')
                                        .format(pickedDate);
                                    // controller.initialDate = pickedDate;
                                    controller.taskDateController.value.text =
                                        formattedDate;
                                  }
                                },
                                focusNode: controller.taskDateFocusNode,
                                controller: controller.taskDateController.value,
                                labelText: "Due Date",
                                readOnly: true,
                                suffixIcon: Padding(
                                  padding: EdgeInsets.all(8.0).copyWith(
                                      right: MySize.getScaledSizeWidth(15)),
                                  child: InkWell(
                                    onTap: () async {
                                      DateTime? pickedDate = await showDatePicker(
                                        context: context,
                                        initialDate: controller.taskDateController
                                            .value.text.isNotEmpty
                                            ? DateFormat('MMMM dd, yyyy').parse(
                                            controller
                                                .taskDateController.value.text)
                                            : DateTime.now(),
                                        firstDate: ((controller.pickDate ?? DateTime.now()) != null && (controller.pickDate ?? DateTime.now()).isBefore(DateTime.now()))
                                            ? (controller.pickDate ?? DateTime.now())
                                            : DateTime.now(),
                                        lastDate: DateTime(2100),
                                      );

                                      if (pickedDate != null) {
                                        log("pick date == ${DateTime(pickedDate.year, pickedDate.month, pickedDate.day)}");
                                        controller.selectedDate = DateTime(pickedDate.year, pickedDate.month, pickedDate.day);
                                        String formattedDate =
                                        DateFormat('MMMM dd, yyyy')
                                            .format(pickedDate);
                                        // controller.initialDate = pickedDate;
                                        controller.taskDateController.value.text =
                                            formattedDate;
                                      }
                                    },
                                    child: Image.asset(AppImage.calender,color: AppTheme.whiteWithNull,),
                                  ),
                                ),
                                textCapitalization: TextCapitalization.words,
                              ),
                              Space.height(30),
                              if (controller.args == null || controller.isBottom.value) ...[
                                Obx(
                                  () => ProjectDropdownField(
                                    controller:
                                        controller.taskProjectNameController.value,
                                    focusNode: controller.taskProjectNameFocusNode,
                                    labelText: "Select a Project",
                                    onTap: () {
                                      // controller.page.value = 1;
                                      controller.searchController.value.clear();
                                      controller.hasMoreData.value = true;
                                      controller.createdProject.clear();
                                      controller.callApiForProjectData(
                                          context: context);
                                      showModalBottomSheet(
                                        context: context,
                                        backgroundColor: Colors.transparent,
                                        isScrollControlled: true,
                                        shape: RoundedRectangleBorder(
                                          borderRadius: BorderRadius.vertical(
                                              top: Radius.circular(40)),
                                        ),
                                        builder: (context) {
                                          final originalSelectedProject =
                                              controller.selectedProjectId.value;
                                          return WillPopScope(
                                            onWillPop: () async {
                                              controller.selectedProjectId.value =
                                                  originalSelectedProject;
                                              return true;
                                            },
                                            child: projectSelection(
                                                controller: controller,
                                                context: context,
                                                originalSelectedProject:
                                                    originalSelectedProject),
                                          );
                                        },
                                      ).then(
                                        (value) {
                                          controller.page.value = 1;
                                        },
                                      );
                                    },
                                  ),
                                ),
                                Space.height(30),
                                Obx(
                                  () => ProjectDropdownField(
                                    controller:
                                        controller.taskSubProjectNameController.value,
                                    focusNode: controller.taskSubProjectNameFocusNode,
                                    labelText: "Select Sub-Project",
                                    enabled: controller.selectedProject.isNotEmpty,
                                    onTap: controller.selectedProject.isNotEmpty ? () {
                                      controller.hasMoreData.value = true;
                                      controller.searchController.value.clear();
                                      controller.subProjectList.clear();
                                      controller.callApiForOneSubProject(
                                          context: Get.context!,
                                          projectId: controller
                                              .selectedProjectId.value
                                              .toString());
                                      showModalBottomSheet(
                                        context: context,
                                        backgroundColor: Colors.transparent,
                                        isScrollControlled: true,
                                        shape: RoundedRectangleBorder(
                                          borderRadius: BorderRadius.vertical(
                                              top: Radius.circular(40)),
                                        ),
                                        builder: (context) {
                                          final originalSelectedProject =
                                              controller.selectedSubProjectId.value;
                                          return WillPopScope(
                                            onWillPop: () async {
                                              controller.selectedSubProjectId.value =
                                                  originalSelectedProject;
                                              return true;
                                            },
                                            child: subProjectSelection(
                                                originalSelectedProject:
                                                    originalSelectedProject,
                                                context: context,
                                                controller: controller),
                                          );
                                        },
                                      ).then(
                                        (value) {
                                          controller.page.value = 1;
                                        },
                                      );
                                    } : () {
                                      CommonFunction.showCustomSnackbar(
                                          message: "Please Select Project First",
                                          isError: true,
                                          backgroundColor: AppTheme.red);
                                    },
                                  ),
                                ),
                                Space.height(30),
                              ],
                              Obx(
                                () => InkWell(
                                  onTap: controller.selectedProject.isNotEmpty ? () {
                                    controller.searchController.value.clear();
                                    if (controller.selectedProject.isNotEmpty) {
                                      controller.callApiForGetProjectUser(
                                          context: context,
                                          projectId: (controller.selectedSubProjectId.value.toString().isNotEmpty &&
                                              controller.selectedSubProjectId.value != null)
                                              ? controller.selectedSubProjectId.value.toString()
                                              : controller.selectedProjectId.value.toString());
                                    }/* else {
                                      controller.callApiForGetTeamUser(
                                          context: context);
                                    }*/

                                    showModalBottomSheet(
                                      context: context,
                                      backgroundColor: Colors.transparent,
                                      isScrollControlled: true,
                                      shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.vertical(
                                            top: Radius.circular(40)),
                                      ),
                                      builder: (context) {
                                        final originalSelectedProject =
                                            controller.selectedUser.value;
                                        return WillPopScope(
                                          onWillPop: () async {
                                            controller.selectedUser.value =
                                                originalSelectedProject;
                                            return true;
                                          },
                                          child: teamUserSelection(
                                              controller: controller,
                                              context: context,
                                              originalSelectedProject:
                                              originalSelectedProject),
                                        );
                                      },
                                    ).then(
                                          (value) {
                                        controller.page.value = 1;
                                      },
                                    );
                                  } : () {
                                    CommonFunction.showCustomSnackbar(
                                        message: "Please Select Project First",
                                        isError: true,
                                        backgroundColor: AppTheme.red);
                                  },
                                  child: Container(
                                    width: double.infinity,
                                    height: MySize.getScaledSizeHeight(66),
                                    decoration: BoxDecoration(
                                      color: AppTheme.appTextField,
                                      border: Border.all(color: AppTheme.borderColor),
                                      borderRadius: BorderRadius.circular(14),
                                    ),
                                    alignment: Alignment.centerLeft,
                                    padding: EdgeInsets.only(
                                        top: MySize.size12 ?? 12,
                                        bottom: MySize.size12 ?? 12,
                                        left: MySize.size20 ?? 12),
                                    child: TypoGraphy(
                                      text: "Assign to a Team Member",
                                      level: 4,
                                      fontWeight: FontWeight.w400,
                                      color: controller.selectedProject.isNotEmpty ? null : AppTheme.grey,
                                    ),
                                  ),
                                ),
                              ),
                              Space.height(20),
                              Obx(
                                    () => controller.selectedUser.value != null
                                    ? Align(
                                  alignment: Alignment.centerLeft,
                                  child: Container(
                                    height: MySize.getScaledSizeHeight(36),
                                    decoration: BoxDecoration(
                                        color: Theme.of(context).brightness == Brightness.dark ? AppTheme.darkBackground : AppTheme.subPrimary,
                                        borderRadius:
                                        BorderRadius.circular(5),border: Border.all(color: AppTheme.white)),
                                    child: Row(
                                      mainAxisSize: MainAxisSize.min,
                                      children: [
                                        Space.width(5),
                                        profileImage(
                                            url: controller.selectedUser.value
                                                ?.image ??
                                                "",
                                            userName: controller.selectedUser
                                                .value?.firstName ??
                                                "",
                                            iconHeight:
                                            MySize.getScaledSizeHeight(
                                                27),
                                            iconWidth:
                                            MySize.getScaledSizeHeight(
                                                27),
                                            width: MySize.getScaledSizeHeight(
                                                27),
                                            height:
                                            MySize.getScaledSizeHeight(
                                                27),
                                            textStyle: TextStyle(
                                                fontSize: 12,
                                                color: AppTheme.white)),
                                        Space.width(6),
                                        TypoGraphy(
                                          text:
                                          "${controller.selectedUser.value?.firstName} ${controller.selectedUser.value?.lastName}",
                                          level: 3,
                                          fontWeight: FontWeight.w400,
                                        ),
                                        Space.width(5),
                                        InkWell(
                                          onTap: () {
                                            controller.selectedUser.value =
                                            null;
                                          },
                                          child: SvgPicture.asset(
                                              AppImage.closeImage),
                                        ),
                                        Space.width(5),
                                      ],
                                    ),
                                  ),
                                )
                                    : SizedBox(),
                              ),
                              Space.height(30),
                              Align(
                                alignment: Alignment.centerLeft,
                                child: TypoGraphy(
                                  text: "Upload Files",
                                  level: 4,
                                ),
                              ),
                              Space.height(4),
                              Align(
                                alignment: Alignment.centerLeft,
                                child: TypoGraphy(
                                  text:
                                      "(PDF, DOC | limit of 3 files)",
                                  level: 3,
                                  color: AppTheme.grey,
                                  fontWeight: FontWeight.w400,
                                ),
                              ),
                              Space.height(20),
                              Container(
                                height: MySize.getScaledSizeHeight(150),
                                width: MySize.getScaledSizeWidth(120),
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(14),
                                  color: Theme.of(context).brightness == Brightness.dark ? AppTheme.darkBackground : AppTheme.subPrimary,
                                ),
                                child: InkWell(
                                  onTap: () => controller.pickFiles(),
                                  child: DottedBorder(
                                    borderType: BorderType.RRect,
                                    dashPattern: const [8, 6],
                                    color: AppTheme.primary1,
                                    borderPadding: EdgeInsets.all(MySize.size1 ?? 1),
                                    radius: Radius.circular(14),
                                    child: Center(
                                      child: Column(
                                        crossAxisAlignment: CrossAxisAlignment.center,
                                        mainAxisAlignment: MainAxisAlignment.center,
                                        children: [
                                          SvgPicture.asset(
                                            AppImage.uploadImage,
                                            height: MySize.size50,
                                            width: MySize.size50,
                                          ),
                                          Space.height(12),
                                          TypoGraphy(
                                            text: "Upload",
                                            level: 4,
                                            fontWeight: FontWeight.w600,
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                              Space.height(25),
                              Obx(() => SizedBox(
                                    height: MySize.getScaledSizeHeight(
                                        controller.imageList.isNotEmpty ? 122 : 0),
                                    child: ListView(
                                      padding: EdgeInsets.only(
                                          top: MySize.getScaledSizeHeight(5)),
                                      scrollDirection: Axis.horizontal,
                                      // mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                      children: [
                                        if (controller.imageList.isNotEmpty) ...[
                                          ...List.generate(controller.imageList.length, (index) {
                                              return Padding(
                                                padding: EdgeInsets.only(right: MySize.getScaledSizeWidth(11)),
                                                child: Stack(
                                                  clipBehavior: Clip.none,
                                                  children: [
                                                    Container(
                                                      height: MySize.getScaledSizeHeight(122),
                                                      width: MySize.getScaledSizeWidth(100),
                                                      decoration: BoxDecoration(
                                                        color: box.read('isDarkMode') ? AppTheme.darkBackground : AppTheme.lightGrey,
                                                        border: Border.all(color: AppTheme.borderWithTrans),
                                                        borderRadius: BorderRadius.circular(14),
                                                      ),
                                                      child: Column(
                                                        mainAxisAlignment: MainAxisAlignment.center,
                                                        children: [
                                                          SvgPicture.asset(
                                                            controller.imageList[index]['originalname'].toString().contains('pdf') ? AppImage.fileTypePdf : AppImage.docImage,
                                                            width: MySize.getScaledSizeWidth(45),
                                                            height: MySize.getScaledSizeHeight(60.08),
                                                          ),
                                                          Space.height(11.92),
                                                          Padding(
                                                            padding: EdgeInsets.symmetric(horizontal: MySize.getScaledSizeWidth(10)),
                                                            child: TypoGraphy(
                                                              text: controller.imageList[index]['originalname'],
                                                              level: 3,
                                                              fontWeight: FontWeight.w500,
                                                              maxLines: 1,
                                                              overflow: TextOverflow.ellipsis,
                                                            ),
                                                          ),
                                                        ],
                                                      ),
                                                    ),
                                                    Positioned(
                                                      right: -10,
                                                      top: -10,
                                                      child: InkWell(
                                                        child: Container(
                                                          height: MySize.size30,
                                                          width: MySize.size30,
                                                          decoration: BoxDecoration(
                                                            color: AppTheme.grey.withOpacity(0.3),
                                                            shape: BoxShape.circle,
                                                          ),
                                                          child: Padding(
                                                            padding: const EdgeInsets.all(7),
                                                            child: SvgPicture.asset(AppImage.closeImage,color: AppTheme.whiteWithNull,),
                                                          ),
                                                        ),
                                                        onTap: () {
                                                          controller.imageList.removeAt(index);
                                                        },
                                                      ),
                                                    )
                                                  ],
                                                ),
                                              );
                                          },),
                                        ],
                                      ],
                                    ),
                                  )),
                            ],
                          ),
                          Space.height(40),
                          Obx(
                            () => Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Buttons(
                                  buttonText: controller.args != null &&
                                      controller.args["isEdit"] != null ? "Update" : "Create",
                                  buttonTextLevel: 4,
                                  isLoading: controller.isLoading.value,
                                  width: MySize.getScaledSizeWidth(198),
                                  height: MySize.getScaledSizeHeight(70),
                                  onTap: () {
                                    String todoName = controller
                                        .taskNameController.value.text
                                        .trim();
                                    String todoDescriptionName = controller
                                        .taskDescriptionController.value.text
                                        .trim();
                                    if (todoName.isEmpty) {
                                      if (controller.taskNameController.value.text
                                              .isNotEmpty &&
                                          controller.taskNameController.value.text
                                              .trim()
                                              .isEmpty) {
                                        CommonFunction.showCustomSnackbar(
                                            message: "Space not allowed",
                                            backgroundColor: AppTheme.red,
                                            isError: true);
                                      } else {
                                        CommonFunction.showCustomSnackbar(
                                          message: "Task Name is Required*",
                                          backgroundColor: AppTheme.red,
                                          isError: true,
                                        );
                                      }

                                      return;
                                    }

                                    if (todoDescriptionName.isEmpty &&
                                        controller.taskDescriptionController.value
                                            .text.isNotEmpty) {
                                      CommonFunction.showCustomSnackbar(
                                          message: "Space not allowed",
                                          backgroundColor: AppTheme.red,
                                          isError: true);
                                      return;
                                    }
                                    if (!RegExp(r'[A-Za-z0-9]').hasMatch(todoName)) {
                                      CommonFunction.showCustomSnackbar(
                                        message:
                                            "Only special characters are not allowed",
                                        backgroundColor: AppTheme.red,
                                        isError: true,
                                      );
                                      return;
                                    }

                                    // Check if the community name contains only numbers
                                    if (RegExp(r'^[0-9 ]+$').hasMatch(todoName)) {
                                      CommonFunction.showCustomSnackbar(
                                        message:
                                            "Todo Name cannot contain only numbers",
                                        backgroundColor: AppTheme.red,
                                        isError: true,
                                      );
                                      return;
                                    }
                                    controller.callApiForCreateToDo(context: context);
                                  },
                                ),
                              ],
                            ),
                          ),
                          Space.height(30)
                        ],
                      ),
                    ),
                  )
                ],
              ),
              Obx(
                    () => controller.isTodoLoading.value &&
                    controller.args != null &&
                    controller.args["isEdit"] != null
                    ? Container(
                  color: AppTheme.black.withValues(alpha: 0.5),
                  child: Loader(),
                )
                    : SizedBox.shrink(),
              ),
            ],
          ),
        );
      }
    );
  }
}
