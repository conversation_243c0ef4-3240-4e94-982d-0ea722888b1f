import 'dart:io';
import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:incenti_ai/app/modules/explore/controllers/explore_controller.dart';
import 'package:incenti_ai/app/modules/profile_view/controllers/profile_view_controller.dart';
import 'package:incenti_ai/constants/app_image.dart';
import 'package:incenti_ai/constants/app_size_constant.dart';
import 'package:incenti_ai/utillites/app_theme.dart';
import 'package:incenti_ai/utillites/current_user.dart';
import 'package:incenti_ai/utillites/loader.dart';

import '../../../../services/app_link_service.dart';
import '../../../../utillites/common_report_bottom_sheet.dart';
import '../../../../utillites/delete_confirmation_dialog.dart';
import '../../../../utillites/typography.dart';
import '../../../routes/app_pages.dart';
import '../../explore/components/common_widget_view.dart';
import '../../profile_view/components/profile_widget_view.dart';
import '../../project_detail_view/controllers/project_detail_view_controller.dart';
import '../../sub_project_detail_view/controllers/sub_project_detail_view_controller.dart';
import '../../user_detail/components/image_picker_bottom_sheet.dart';
import '../components/post_detail_widget.dart';
import '../components/post_action_view.dart';
import '../controllers/post_detail_view_controller.dart';

class PostDetailView extends StatelessWidget {
  const PostDetailView({super.key});

  @override
  Widget build(
    BuildContext context,
  ) {
    return GetBuilder<PostDetailViewController>(
        init: PostDetailViewController(),
        tag: Get.arguments != null ? Get.arguments['postID']?.toString() : null,
        builder: (controller) {
          return Scaffold(
            // backgroundColor: AppTheme.white,
            body: SafeArea(
              bottom: false,
              child: Stack(
                children: [
                  Obx(
                    () => controller.isLoading.value
                        ? Center(
                            child: Loader(),
                          )
                        : controller.isPostNotFound.value
                            ? Padding(
                                padding: EdgeInsets.symmetric(
                                    horizontal: MySize.getScaledSizeWidth(30)),
                                child: Column(
                                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    buildIcon(AppImage.backArrow, () {
                                      print("route == ${Get.previousRoute}");
                                      Get.back();
                                    }, padding: 7,color:
                                    AppTheme.whiteWithBase,),
                                    Column(
                                      mainAxisAlignment:
                                          MainAxisAlignment.center,
                                      children: [
                                        Center(
                                          child: Image.asset(
                                            "assets/images/not_folder.png",
                                            width: MySize.getScaledSizeWidth(
                                                200),
                                            height:
                                                MySize.getScaledSizeHeight(
                                                    200),
                                          ),
                                        ),
                                        Space.height(20),
                                        Center(
                                          child: TypoGraphy(
                                            text: "Post not found!",
                                            level: 4,
                                          ),
                                        ),
                                      ],
                                    ),
                                    SizedBox.shrink(),
                                  ],
                                ),
                              )
                            : Padding(
                                padding: EdgeInsets.symmetric(
                                    horizontal: MySize.getScaledSizeWidth(25)),
                                child: SingleChildScrollView(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Space.height(60),
                                      Column(
                                        mainAxisAlignment:
                                            MainAxisAlignment.start,
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          postHeadView(
                                              controller
                                                  .getPostDetailData.value,
                                              context),
                                          postSubProject(controller
                                              .getPostDetailData.value),
                                          postDetail(
                                              controller
                                                  .getPostDetailData.value,
                                              context)
                                        ],
                                      ),
                                      Space.height(100),
                                    ],
                                  ),
                                ),
                              ),
                  ),
                  Obx(
                    () => (controller.isPostNotFound.value || controller.isLoading.value) ? SizedBox.shrink() :
                    Positioned(
                      left: MySize.getScaledSizeWidth(25),
                      right: MySize.getScaledSizeWidth(25),
                      top:
                          MySize.getScaledSizeHeight(Platform.isAndroid ? 10 : 0),
                      child: Row(
                        children: [
                          InkWell(
                            child: ClipRRect(
                              borderRadius: BorderRadius.circular(50),
                              child: BackdropFilter(
                                filter: ImageFilter.blur(
                                  sigmaX: 5,
                                  sigmaY: 5,
                                ),
                                child: SizedBox(
                                  height: MySize.getScaledSizeHeight(40),
                                  width: MySize.getScaledSizeWidth(40),
                                  child: Padding(
                                    padding: EdgeInsets.all(9),
                                    child: SvgPicture.asset(
                                      AppImage.closeImage,
                                      height: MySize.getScaledSizeHeight(28),
                                      width: MySize.getScaledSizeWidth(28),
                                      color: AppTheme.whiteWithNull,
                                    ),
                                  ),
                                ),
                              ),
                            ),
                            onTap: () {
                              // if(controller.args != null &&
                              //     controller.args["isDetailOpened"] != null) {
                              //   Get.close(2);
                              // } else {
                              Get.back();
                              // }
                            },
                          ),
                          Spacer(),
                          if (!(controller.args != null &&
                              controller.args["isFromNotification"] != null))
                            InkWell(
                              onTap: () {
                                HapticFeedback.lightImpact();
                                ImagePickerBottomSheet.show(
                                    context: context,
                                    child: showPostDetailOption(
                                      postId: controller.postId.value,
                                      isUser: CurrentUser.user.id ==
                                          controller
                                              .getPostDetailData.value.user?.id,
                                      callBack: () {
                                        controller.callApiForGetOnePost(
                                            context: Get.context!);
                                      },
                                      onTap: () {
                                        Navigator.pop(context);
                                        Get.toNamed(Routes.post, arguments: {
                                          "PostId": controller.postId.value,
                                          "isPostEdit": true,
                                          "isCrossVisible": false,
                                          if (controller.source.value ==
                                              "community")
                                            "isCommunity": true,
                                          if (controller.source.value ==
                                              "community")
                                            "isCommunityEdit": true,
                                          if (controller.source.value ==
                                              "community")
                                            "isCommunityDetail": true,
                                        })?.then(
                                          (value) {
                                            controller.callApiForGetOnePost(
                                                context: Get.context!);
                                          },
                                        );
                                      },
                                      reportPostOnTap: () {
                                        Navigator.pop(context);
                                        showCommonReportBottomSheet(
                                          context: context,
                                          title: "Report",
                                          subTitle:
                                              "Why are you reporting this post?",
                                          description:
                                              "Your report is anonymous. If someone is in\nimmediate danger, call the local emergency\nservices - don't wait.",
                                          options: controller.repostData,
                                          onOptionTap: (selectedOption) async {
                                            controller.selectedReason.value =
                                                selectedOption;
                                            await controller.callApiForReportPost(
                                              context: context,
                                              postId: controller.postId.value
                                                  .toString(),
                                            );
                                          },
                                        );
                                      },
                                      onShareTap: () {
                                        print('button tapped ==> ');
                                        Navigator.pop(context);
                                        HapticFeedback.lightImpact();
                                        AppLinkService().shareMedia(
                                            slug: controller.getPostDetailData
                                                    .value.slug ??
                                                "",
                                            mediaType: ShareMediaType.posts,
                                            title: controller
                                                .getPostDetailData.value.title);
                                      },
                                      deleteOnTap: () async {
                                        Navigator.pop(Get.context!);
                                        HapticFeedback.heavyImpact();
                                        final pvc = Get.isRegistered<
                                            ProfileViewController>()
                                            ? Get.find<
                                            ProfileViewController>()
                                            : Get.put(
                                            ProfileViewController());
                                        showDeleteConfirmationDialog(
                                          context: Get.context!,
                                          description:
                                              "Are you sure you want to delete Post permanently?",
                                          onConfirm: () async {
                                            await pvc
                                                .callApiForDeleteOnePost(
                                                    context: Get.context!,
                                                    postId:
                                                        controller.postId.value)
                                                .then(
                                              (value) {
                                                pvc.hasMoreData.value = true;
                                                pvc.page.value = 1;
                                                pvc.callApiForUserGetPost(
                                                    context: Get.context!);
                                                if (Get.isRegistered<
                                                    ProjectDetailViewController>()) {
                                                  Get.find<
                                                          ProjectDetailViewController>()
                                                      .page
                                                      .value = 1;
                                                  Get.find<
                                                          ProjectDetailViewController>()
                                                      .hasMoreData
                                                      .value = true;
                                                  Get.find<
                                                          ProjectDetailViewController>()
                                                      .postDataList
                                                      .clear();
                                                  Get.find<
                                                          ProjectDetailViewController>()
                                                      .callApiForUserGetPost(
                                                    context: Get.context!,
                                                  );
                                                }
                                                if (Get.isRegistered<
                                                    SubProjectDetailViewController>()) {
                                                  Get.find<
                                                          SubProjectDetailViewController>()
                                                      .page
                                                      .value = 1;
                                                  Get.find<
                                                          SubProjectDetailViewController>()
                                                      .hasMoreData
                                                      .value = true;
                                                  Get.find<
                                                          SubProjectDetailViewController>()
                                                      .postDataList
                                                      .clear();
                                                  Get.find<
                                                          SubProjectDetailViewController>()
                                                      .callApiForUserGetPost(
                                                    context: Get.context!,
                                                  );
                                                }
                                                Get.find<ExploreController>()
                                                    .hasMoreData
                                                    .value = true;
                                                Get.find<ExploreController>()
                                                    .page
                                                    .value = 1;
                                                Get.find<ExploreController>()
                                                    .postDataList
                                                    .clear();
                                                Get.find<ExploreController>()
                                                    .callApiForExplorePost(
                                                        context: context);

                                                Navigator.pop(Get.context!);
                                              },
                                            );
                                          },
                                          title: "Delete Post",
                                          onCancel: () {
                                            Get.back();
                                          }, isLoading: pvc.isLoading,
                                        );
                                      },
                                    ));
                              },
                              child: ClipRRect(
                                borderRadius: BorderRadius.circular(50),
                                child: BackdropFilter(
                                  filter: ImageFilter.blur(
                                    sigmaX: 5,
                                    sigmaY: 5,
                                  ),
                                  child: SizedBox(
                                    height: MySize.size40,
                                    width: MySize.size40,
                                    child: Padding(
                                      padding: EdgeInsets.all(7),
                                      child: SvgPicture.asset(
                                        AppImage.moreVertIcon,
                                        height: MySize.getScaledSizeHeight(28),
                                        width: MySize.getScaledSizeWidth(28),
                                        color: AppTheme.whiteWithNull,
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                            ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
            // floatingActionButtonLocation: FloatingActionButtonLocation.centerDocked,
            bottomNavigationBar: !(controller.args != null &&
                    controller.args["isFromNotification"] != null)
                ? Obx(() => (controller.isLoading.value ||
                        controller.isPostNotFound.value)
                    ? SizedBox()
                    : Padding(
                        padding: EdgeInsets.only(
                            bottom: Platform.isAndroid ? 0 : 15),
                        child: CommonPostActions(
                          controller: controller,
                          source: controller.source.value,
                          postId: controller.postId.value,
                          index: controller.index.value,
                          postDetailData: controller.getPostDetailData,
                          projectId: controller.projectId.value,
                        ),
                      ))
                : SizedBox(),
          );
        });
  }
}
