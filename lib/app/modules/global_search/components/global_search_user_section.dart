import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';

import '../../../../constants/app_size_constant.dart';
import '../../../../models/app_other_user_model.dart';
import '../../../../models/section_base.dart';
import '../../../../utillites/app_theme.dart';
import '../../../../utillites/common_profile_widget.dart';
import '../../../../utillites/current_user.dart';
import '../../../../utillites/custom_sliver_list_view.dart';
import '../../../../utillites/empty.dart';
import '../../../../utillites/typography.dart';
import '../../../routes/app_pages.dart';
import '../../profile_view/components/remover_follower.dart';
import '../../project_detail_view/components/team_shimmer_effect.dart';
import '../controllers/global_search_view_controller.dart';

class GlobalSearchUserSection extends SectionBase<GlobalSearchViewController> {
  GlobalSearchUserSection({required super.controller});

  @override
  String get title => 'Users';

  @override
  Widget floatingActionButtonBuilder(BuildContext context) {
    return SizedBox();
  }

  @override
  List<Widget> viewBuilder(BuildContext context) {
    return [
      Obx(() {
        return controller.isLoading.value &&
            controller.projectUserList.isEmpty
            ? SliverToBoxAdapter(
            child: ListView.builder(
              itemCount: 25,
              shrinkWrap: true,
              padding:
              EdgeInsets.only(top: MySize.size10 ?? 10),
              controller: ScrollController(),
              itemBuilder: (context, index) {
                return TeamMemberTileShimmer();
              },
            ))
            : controller.searchController.value.text
            .isNotEmpty &&
            controller.projectUserList.isEmpty
            ? SliverToBoxAdapter(
          child: Padding(
            padding: EdgeInsets.only(
                top: MySize.getScaledSizeHeight(250)),
            child: Column(
              mainAxisAlignment:
              MainAxisAlignment.center,
              children: [
                SvgPicture.asset(
                  "assets/images/icon_search.svg",
                  height: MySize.size50,
                  color: AppTheme.whiteWithNull,
                ),
                Empty(
                  title: "Search Result Not Found !",
                ),
              ],
            ),
          ),
        )
            : CustomSliverListView(
          emptyWidget: Padding(
            padding: EdgeInsets.only(
              top: MySize.getScaledSizeHeight(150),
              left: paddingHoriZontal,
              right: paddingHoriZontal,
            ),
            child:Column(
              crossAxisAlignment:
              CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                SvgPicture.asset('assets/svg/search_illustrator.svg'),
                Space.height(20),
                TypoGraphy(
                  text: "you can search here.",
                  level: 5,
                ),
                Space.height(5),
                TypoGraphy(
                  text: "Smart & Fast Results",
                  level: 3,
                  color: AppTheme.grey,
                )
              ],
            ),
          ),
          maximumReachedWidget: const SizedBox(),
          itemBuilder:
              (context, OtherUser otherUser, index) {
            return InkWell(
              onTap: () {
                Get.toNamed(Routes.other_user_profile,arguments: {"UserId": otherUser.id});
              },
              child: Padding(
                padding: EdgeInsets.symmetric(vertical: MySize.getScaledSizeHeight(8)),
                child: ListTile(
                    contentPadding: EdgeInsets.symmetric(
                        horizontal:
                        MySize.getScaledSizeWidth(30)),
                    leading: profileImage(
                      url: controller
                          .projectUserList[index].image ??
                          "",
                      userName: controller
                          .projectUserList[index]
                          .firstName ??
                          "",
                      iconHeight:
                      MySize.getScaledSizeHeight(60),
                      iconWidth: MySize.getScaledSizeWidth(60),
                      height: MySize.getScaledSizeHeight(60),
                      width: MySize.getScaledSizeWidth(60),
                    ),
                    title: TypoGraphy(
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                      text:
                      "${controller.projectUserList[index].firstName} ${controller.projectUserList[index].lastName}",
                      level: 12,
                      // color: AppTheme.baseBlack,
                    ),
                    // subtitle: TypoGraphy(
                    //   maxLines: 1,
                    //   overflow: TextOverflow.ellipsis,
                    //   text: controller
                    //       .projectUserList[index].email,
                    //   level: 2,
                    //   fontWeight: FontWeight.w400,
                    //   color: AppTheme.grey,
                    // ),
                    trailing: controller
                        .projectUserList[index].id == CurrentUser.user.id ? SizedBox.shrink() : Obx(
                          () => InkWell(
                        onTap: () {
                          if (controller
                              .projectUserList[
                          index].isFollowing
                              ?.value ==
                              false) {
                            showModalBottomSheet(
                              backgroundColor:
                              Colors
                                  .transparent,
                              context: context,
                              builder: (context) {
                                return RemoveFollower(
                                  userName:
                                  "${controller.projectUserList[index].firstName} ${controller.projectUserList[index].lastName}",
                                  image: controller
                                      .projectUserList[
                                  index].image ??
                                      "",
                                  onRemove: () {
                                    controller.callApiForUnFollowUser(
                                        context: Get
                                            .context!,
                                        userId: controller
                                            .projectUserList[
                                        index].id,
                                        isUnFollowed:
                                        true,
                                        index:
                                        index);
                                  },
                                  isForUnfollow:
                                  true,
                                );
                              },
                            );
                          } else {
                            controller
                                .projectUserList[
                            index].isFollowing
                                ?.value = false;
                            controller.callApiForFollowUser(
                                context:
                                Get.context!,
                                userId: controller
                                    .projectUserList[
                                index].id);
                          }
                        },
                        child: Container(
                          decoration:
                          BoxDecoration(
                            borderRadius:
                            BorderRadius
                                .circular(50),
                            color: (Theme.of(context)
                                .brightness ==
                                Brightness
                                    .dark &&
                                !(controller
                                    .projectUserList[
                                      index].isFollowing
                                ?.value ==
                                    false))
                                ? AppTheme.bottomBar
                                : Colors.transparent,
                            border: Border.all(
                                color: controller
                                    .projectUserList[
                                index].isFollowing
                                    ?.value ==
                                    false
                                    ? AppTheme
                                    .grey
                                    : Theme.of(context)
                                    .brightness ==
                                    Brightness
                                        .dark
                                    ? AppTheme.grey
                                    : AppTheme
                                    .primary1),
                          ),
                          child: Padding(
                            padding: EdgeInsets
                                .symmetric(
                              horizontal: MySize
                                  .getScaledSizeWidth(
                                  17),
                              vertical: MySize
                                  .getScaledSizeHeight(
                                  11),
                            ),
                            child: TypoGraphy(
                              text: controller
                                  .projectUserList[
                              index].isFollowing
                                  ?.value ==
                                  false
                                  ? "Unfollow"
                                  : "Follow",
                              textStyle:
                              TextStyle(
                                fontFamily:
                                "Inter",
                                color: controller
                                    .projectUserList[
                                index].isFollowing
                                    ?.value ==
                                    false
                                    ? AppTheme
                                    .grey
                                    : AppTheme
                                    .primaryIconDark,
                                fontSize: MySize
                                    .getScaledSizeHeight(
                                    15),
                                fontWeight:
                                FontWeight
                                    .w600,
                              ),
                            ),
                          ),
                        ),
                      ),
                    )),
              ),
            );
          },
          isLoading: controller.isLoading.value,
          items: controller.projectUserList,
          hasMoreData: controller.hasMoreData.value,
          onLoadMore: () {
            return controller.callApiForGetTeamUser(
              context: context,
            );
          },
        );
      })
    ];
  }

  @override
  void onCategorySelected() {
    // controller.page.value = 1;
    controller.hasMoreData.value = false;
    controller.projectUserList.clear();
    // controller.callApiForGetTeamUser(context: Get.context!);
  }
}
