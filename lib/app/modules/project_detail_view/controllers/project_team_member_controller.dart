import 'dart:async';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:incenti_ai/constants/api.dart';
import 'package:incenti_ai/services/api_manager.dart';
import 'package:incenti_ai/utillites/common_function.dart';

import '../../../../models/app_other_user_model.dart';
import '../../../../models/app_project_user_member_model.dart';

class ProjectTeamMemberController extends GetxController {
  Rx<TextEditingController> searchController = TextEditingController().obs;
  FocusNode searchFocusNode = FocusNode();
  RxInt selectedIndex = 0.obs;
  var args = Get.arguments;
  ApiManager apiManager = ApiManager();
  RxBool isLoading = false.obs;
  RxBool isSubProject = false.obs;
  RxBool hasMoreData = true.obs;
  var hasInitialData = false.obs;
  RxList<OtherUser> projectUserList = <OtherUser>[].obs;

  RxInt projectId = (-1).obs;
  RxInt parentProjectId = (-1).obs;
  int limit = 10;
  RxInt page = 1.obs;
  RxInt createdBy = 0.obs;
  Timer? debounceTimer;
  RxMap<int, bool> selectedUsersMap = <int, bool>{}.obs;

  @override
  void onInit() {
    // TODO: implement onInit
    if (args != null && args["projectId"] != null) {
      projectId.value = args["projectId"];
    }
    if (args != null && args["parentProjectId"] != null) {
      parentProjectId.value = args["parentProjectId"];
    }

    if (args != null && args["isSubProject"] != null) {
      isSubProject.value = args["isSubProject"];
    }
    if(args != null && args['createdBy'] != null ) {
      createdBy.value = args['createdBy'];
    }

    // Don't call API on init - only when user searches
    // Set hasInitialData to true so UI shows search field
    hasInitialData.value = true;

    super.onInit();
  }

  pullRefresh() {
    // Only call API if there's a search query
    if (searchController.value.text.trim().isNotEmpty) {
      page.value = 1;
      hasMoreData.value = true;
      callApiForGetProjectUser(
        context: Get.context!,
        projectId: projectId.value.toString(),
        isSubProject: isSubProject.value,
        parentProjectId: parentProjectId.value.toString(),
      );
    } else {
      // Clear the list if search is empty
      projectUserList.clear();
      hasMoreData.value = true;
      page.value = 1;
    }
  }

  Future<void> callApiForGetProjectUser(
      {required BuildContext context,
      required String projectId,
      required String parentProjectId,
      required bool isSubProject}) {

    // Only make API call if there's a search query
    if (searchController.value.text.trim().isEmpty) {
      isLoading.value = false;
      return Future.value();
    }

    isLoading.value = true;

    return apiManager.callApi(
      APIS.projectMember.getAllProjectMember,
      params: {
        "ProjectId": projectId,
        "searchQuery": searchController.value.text, // Always include search query
        if (isSubProject) "ParentId": parentProjectId,
        // "limit": limit,
        // "page": page.value,
      },
      successCallback: (response, message) async {
        try {
          if (response['status'] == 'success') {
            ProjectUserMemberResponse projectMemberResponse =
                ProjectUserMemberResponse.fromJson(response);
            final newData = projectMemberResponse.data.data;
            final isLastPage = newData.length < limit;
            hasMoreData.value = !isLastPage;

            if (page.value == 1) {
              projectUserList.value = newData;
            } else {
              projectUserList.addAll(newData);
            }

            page.value++;
            // if (projectMemberResponse.data.data.length < limit) {
            //   hasMoreData.value = false;
            // }
            // projectUserList.addAll(projectMemberResponse.data.data);
            // if (projectUserList.isEmpty) {
            //   hasMoreData.value = false;
            // } else {
            //   hasMoreData.value = true;
            // }
            // page.value++;
            // isLoading.value = false;
          }
        } catch (error) {
          hasMoreData.value = false;
        } finally {
          isLoading.value = false;
        }
      },
      failureCallback: (message, statusCode) {
        hasMoreData.value = false;
        isLoading.value = false;
      },
    );
  }

  Future<void> callApiForInviteProjectUser(
      {required BuildContext context, required List projectMemberList}) async {
    isLoading.value = true;
    Map<String, dynamic> dict = {
      "UserIds": projectMemberList,
      "ProjectId": projectId.value.toString(),
      "access": selectedIndex.value == 0 ? "read" : "write",
    };
    return apiManager.callApi(
      APIS.projectMember.inviteMember,
      params: dict,
      successCallback: (response, message) async {
        try {
          if (response['status'] == 'success') {
            Get.close(2);
            CommonFunction.showCustomSnackbar(message: response['message']);
            isLoading.value = false;
          }
        } catch (error) {
          isLoading.value = false;
        }
      },
      failureCallback: (message, statusCode) {
        isLoading.value = false;
      },
    );
  }
}
