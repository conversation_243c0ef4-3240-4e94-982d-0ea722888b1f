import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:incenti_ai/app/modules/setting/controllers/setting_controller.dart';
import 'package:incenti_ai/utillites/app_theme.dart';
import 'package:incenti_ai/utillites/typography.dart';

import '../../../../constants/app_image.dart';
import '../../../../constants/app_size_constant.dart';
import '../../../../main.dart';
import '../../../../utillites/buttons.dart';
import '../../../routes/app_pages.dart';

class CustomNavigationView extends StatelessWidget {
  final bool isFromSignUp;

  const CustomNavigationView({super.key, this.isFromSignUp = false});

  @override
  Widget build(BuildContext context) {
    final settingController = Get.put(SettingController());
    return Scaffold(
      body: SafeArea(
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: MySize.size30 ?? 30),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Space.height(10),
              Row(
                children: [
                  if (!isFromSignUp)
                    Align(
                      alignment: Alignment.centerLeft,
                      child: InkWell(
                        onTap: () {
                          Get.back();
                        },
                        child: SvgPicture.asset(
                          AppImage.backArrow,
                          height: MySize.getScaledSizeHeight(28),
                          width: MySize.getScaledSizeHeight(28),color: AppTheme.whiteWithNull,
                        ),
                      ),
                    ),
                  Spacer(),
                  if (isFromSignUp)
                    InkWell(
                      onTap: () {
                        Get.toNamed(Routes.create_project,
                            arguments: {"isFromSignUp": true});
                      },
                      child: TypoGraphy(
                        text: "Skip",
                        level: 4,
                      ),
                    ),
                ],
              ),
              Space.height(10),
              TypoGraphy(
                text: "Customise Navigation",
                level: 8,
                fontWeight: FontWeight.w700,
              ),
              Space.height(10),
              TypoGraphy(
                text: "Select your first home option by priority.",
                level: 3,
                fontWeight: FontWeight.w400,
                color: AppTheme.grey,
              ),
              Space.height(30),
              ...settingController.options.map(
                (option) {
                  return Obx(
                    () => Container(
                      margin: EdgeInsets.only(bottom: 10),
                      height: MySize.getScaledSizeHeight(64),
                      decoration: BoxDecoration(
                        color: box.read('isDarkMode') ? AppTheme.baseBlack : AppTheme.lightGrey,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: RadioListTile<String>(
                        value: option['title'],
                        groupValue: settingController.selectedOption.value,
                        onChanged: (value) {
                          settingController.selectedOption.value = value!;
                        },
                        title: Row(
                          children: [
                            SvgPicture.asset(
                              color: box.read('isDarkMode') ? Colors.white : null,
                              option['icon'],
                              height: MySize.getScaledSizeHeight(24),
                              width: MySize.getScaledSizeWidth(24),
                            ),
                            Space.width(13),
                            TypoGraphy(
                              text: option['title'] == "Explore"
                                  ? "Feed"
                                  : option['title'],
                              textStyle: TextStyle(
                                  fontSize: 15,
                                  fontWeight: FontWeight.w500,
                                  fontFamily: "Inter"),
                            )
                          ],
                        ),
                        activeColor: AppTheme.primary1,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                    ),
                  );
                },
              ),
              Space.height(20),
              Obx(
                () => Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Buttons(
                      isLoading: settingController.isLoading.value,
                      onTap: () {
                        // Get.back();
                        HapticFeedback.lightImpact();
                        settingController.callApiForChangeNavigation(
                            context: context,isFromSignUp: isFromSignUp);
                      },
                      buttonText: "Update",
                      buttonTextLevel: 4,
                      width: MySize.getScaledSizeHeight(189),
                    ),
                  ],
                ),
              ),
              // Space.height(198),
            ],
          ),
        ),
      ),
    );
  }
}
