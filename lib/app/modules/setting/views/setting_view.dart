import 'dart:ui';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:incenti_ai/app/modules/user_detail/components/image_picker_bottom_sheet.dart';
import 'package:incenti_ai/constants/app_image.dart';
import 'package:incenti_ai/utillites/app_theme.dart';
import 'package:incenti_ai/utillites/buttons.dart';
import 'package:incenti_ai/utillites/common_function.dart';
import 'package:incenti_ai/utillites/typography.dart';
import 'package:restart_app/restart_app.dart';

import '../../../../constants/app_size_constant.dart';
import '../../../../main.dart';
import '../../../../utillites/current_user.dart';
import '../../../../utillites/theme_controller.dart';
import '../../../routes/app_pages.dart';
import '../controllers/setting_controller.dart';

class SettingView extends GetWidget<SettingController> {
  const SettingView({super.key});

  @override
  Widget build(BuildContext context) {
    MySize().init(context);
    return Scaffold(
      // backgroundColor: AppTheme.white,
      resizeToAvoidBottomInset: false,
      body: Padding(
        padding: EdgeInsets.symmetric(horizontal: MySize.size30 ?? 30),
        child: Column(
          children: [
            Space.height(60),
            Align(
              alignment: Alignment.centerRight,
              child: InkWell(
                onTap: () {
                  Get.back();
                },
                child: SvgPicture.asset(
                  AppImage.closeImage,
                  height: MySize.getScaledSizeHeight(20),
                  width: MySize.getScaledSizeHeight(20),
                  colorFilter: ColorFilter.mode(
                    AppTheme.getIconColor(context),
                    BlendMode.srcIn,
                  ),
                ),
              ),
            ),
            Space.height(2),
            TypoGraphy(
              text: "Settings",
              level: 8,
              fontWeight: FontWeight.w700,
            ),
            Space.height(20),
            ...controller.settingOption.map(
              (e) => Padding(
                padding: EdgeInsets.symmetric(vertical: MySize.size10 ?? 10),
                child: InkWell(
                  onTap: () {
                    controller.oldPasswordController.clear();
                    controller.newPasswordController.clear();
                    controller.confirmPasswordController.clear();
                    controller.isError.value = false;
                    controller.passwordConditionsMet.value =
                        List.generate(4, (index) => false);
                    if (CurrentUser.user.customSetting != null) {
                      controller.selectedOption.value = CurrentUser
                              .user.customSetting?.homeSection?.capitalize ??
                          "explore";
                    }
                    var onTapFunction = e["onTap"];
                    if (onTapFunction != null) {
                      onTapFunction();
                    }
                  },
                  child: Row(
                    children: [
                      Container(
                        height: MySize.size46,
                        width: MySize.size46,
                        padding: e["image"] == AppImage.onboardingLogo ||
                                e["image"] == AppImage.nightMode
                            ? EdgeInsets.all(5)
                            : null,
                        decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            color: e["image"] == AppImage.onboardingLogo ||
                                    e["image"] == AppImage.nightMode
                                ? box.read('isDarkMode') ? Color(0xFF1A1630) : AppTheme.subPrimary
                                : null),
                        child: SvgPicture.asset(
                          e["image"],
                          height: MySize.size46,
                          width: MySize.size46,
                        ),
                      ),
                      Space.width(20),
                      TypoGraphy(
                        text: e["name"],
                        level: 12,
                        fontWeight: FontWeight.w600,
                      ),
                      Spacer(),
                      if (e["image"] == AppImage.nightMode)
                        Obx(
                          () => CupertinoSwitch(
                            // This bool value toggles the switch.
                            value: controller.switchValue.value,
                            activeTrackColor: CupertinoColors.activeBlue,
                            onChanged: (value) {
                              controller.switchValue.value =
                                  !controller.switchValue.value;
                              showThemeDialog(context: context,switchValue: controller.switchValue.value);
                              // Get.find<ThemeController>().changeTheme();
                            },
                          ),
                        )
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
      floatingActionButtonLocation: FloatingActionButtonLocation.centerDocked,
      floatingActionButton: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Buttons(
                onTap: () {
                  HapticFeedback.heavyImpact();
                  ImagePickerBottomSheet.show(
                    context: context,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Space.height(19),
                        TypoGraphy(
                          text: "Sign Out?",
                          level: 6,
                        ),
                        Space.height(8),
                        TypoGraphy(
                          text: "Would you like to sign out of\nyour account?",
                          level: 4,
                          textAlign: TextAlign.center,
                          fontWeight: FontWeight.w400,
                        ),
                        Space.height(30),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            InkWell(
                              child: TypoGraphy(
                                text: "Not Now",
                                level: 4,
                              ),
                              onTap: () {
                                Get.back();
                              },
                            ),
                            Space.width(40),
                            Buttons(
                              buttonText: "Yes, Sign Out",
                              buttonTextLevel: 4,
                              color: AppTheme.red,
                              height: MySize.size70 ?? 70,
                              width: MySize.getScaledSizeWidth(170),
                              onTap: () {
                                HapticFeedback.heavyImpact();
                                Get.closeAllSnackbars();
                                CommonFunction.logOut();
                              },
                            )
                          ],
                        ),
                        Space.height(30),
                      ],
                    ),
                  );
                },
                color: AppTheme.red,
                buttonTextLevel: 4,
                buttonText: "Sign Out",
                height: MySize.getScaledSizeHeight(68),
                width: MySize.getScaledSizeWidth(189),
              ),
            ],
          ),
          Space.height(35),
          InkWell(
            onTap: () {
              HapticFeedback.heavyImpact();
              ImagePickerBottomSheet.show(
                context: context,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Space.height(20),
                    TypoGraphy(
                      text: "Delete Account?",
                      level: 6,
                    ),
                    Space.height(8),
                    TypoGraphy(
                      text:
                          "If you delete your account, all your\ninformation will be removed from\nthe platform permanently.",
                      level: 4,
                      textAlign: TextAlign.center,
                      fontWeight: FontWeight.w400,
                    ),
                    Space.height(30),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        InkWell(
                          child: TypoGraphy(
                            text: "Not Now",
                            level: 4,
                          ),
                          onTap: () {
                            Get.back();
                          },
                        ),
                        Space.width(40),
                        Buttons(
                          buttonText: "Yes, Delete",
                          buttonTextLevel: 4,
                          color: AppTheme.red,
                          height: MySize.size70 ?? 70,
                          width: MySize.getScaledSizeWidth(170),
                          onTap: () {
                            HapticFeedback.heavyImpact();
                            Get.closeAllSnackbars();
                            CurrentUser.deleteMe(callback: () {
                              CommonFunction.logOut();
                            });
                          },
                        )
                      ],
                    ),
                    Space.height(30),
                  ],
                ),
              );
            },
            child: TypoGraphy(
              text: "Delete Account?",
              level: 4,
            ),
          ),
          Space.height(49),
          TypoGraphy(
            text: "Version: 1.1",
            level: 3,
            color: AppTheme.grey,
            fontWeight: FontWeight.w400,
          ),
          Space.height(20),
        ],
      ),
    );
  }

}

showThemeDialog({
  required BuildContext context,
  bool switchValue = false,
  String? title,
  String? message,
  void Function()? onConfirm,
  void Function()? onCancel,
}) {
  showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) {
        return BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 5, sigmaY: 5),
          child: Dialog(
            // insetPadding:
            // EdgeInsets.symmetric(horizontal: MySize.getScaledSizeWidth(70)),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(15),
            ),
            child: Padding(
              padding: EdgeInsets.all(MySize.getScaledSizeHeight(20)),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisSize: MainAxisSize.min,
                children: [
                  TypoGraphy(
                    text: title ?? 'Change App Theme?',
                    level: 4,
                    fontWeight: FontWeight.w600,
                  ),
                  Space.height(20),
                  Padding(
                    padding: EdgeInsets.symmetric(horizontal: MySize.getScaledSizeWidth(20)),
                    child: TypoGraphy(
                      textAlign: TextAlign.center,
                      text:
                      message ?? 'Are you sure you want to switch the app theme? You can change it back anytime in settings.',
                      level: 4,
                      fontWeight: FontWeight.w400,
                    ),
                  ),
                  Space.height(30),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Expanded(
                        child: InkWell(
                          onTap: onCancel ?? () {
                            switchValue =
                            !switchValue;
                            Get.back();
                          },
                          child: Padding(
                            padding: EdgeInsets.only(left: MySize.getScaledSizeWidth(10)),
                            child: TypoGraphy(
                              text: "No",
                              level: 4,
                              textAlign: TextAlign.center,
                            ),
                          ),
                        ),
                      ),
                      Expanded(
                        child: Buttons(
                          buttonText: "Yes",
                          buttonTextLevel: 4,
                          height: MySize.size70 ?? 70,
                          // width: MySize.getScaledSizeWidth(150),
                          onTap:onConfirm ?? () {
                            HapticFeedback.heavyImpact();
                            Get.find<ThemeController>().changeTheme();
                            // Restart.restartApp();
                            Get.offAllNamed(Routes.SPLASH);
                          },
                        ),
                      )
                    ],
                  ),
                ],
              ),
            ),
          ),
        );
      });
}
