import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:image_cropper/image_cropper.dart';
import 'package:image_picker/image_picker.dart';
import 'package:incenti_ai/constants/api.dart';
import 'package:incenti_ai/services/api_manager.dart';
import 'package:incenti_ai/utillites/typography.dart';
import 'package:permission_handler/permission_handler.dart';
import '../app/modules/splash/views/splash_view.dart';
import '../app/routes/app_pages.dart';
import '../constants/app_image.dart';
import '../constants/app_size_constant.dart';
import '../constants/constant.dart';
import '../main.dart';
import '../services/app_notification_handler.dart';
import 'app_theme.dart';
import 'current_user.dart';
import 'package:http/http.dart' as http;

class CommonFunction {
  static Map<String, Style> style() {
    return {
      "p": Style(
        lineHeight: const LineHeight(1.5),
        padding: HtmlPaddings.zero,
        fontSize: FontSize(MySize.size14 ?? 14),
        margin: Margins.zero,
      ),
      "h3": Style(
        padding: HtmlPaddings.only(top: 0.5),
        margin: Margins.zero,
        fontSize: FontSize(MySize.size18 ?? 18),
        fontWeight: FontWeight.w600,
      ),
      "h4": Style(
        padding: HtmlPaddings.only(bottom: 0, top: 0.9, right: 0, left: 0),
        margin: Margins.zero,
        fontSize: FontSize(MySize.size16 ?? 16),
        fontWeight: FontWeight.w600,
      ),
      "h2": Style(
        padding: HtmlPaddings.only(bottom: 0, top: 0.9, right: 0, left: 0),
        margin: Margins.zero,
        fontSize: FontSize(MySize.size20 ?? 20),
        fontWeight: FontWeight.w600,
      ),
      "h1": Style(
        padding: HtmlPaddings.only(bottom: 0, top: 0.9, right: 0, left: 0),
        margin: Margins.zero,
        fontSize: FontSize(MySize.size22 ?? 22),
        fontWeight: FontWeight.w800,
      ),
      "p span": Style(
        lineHeight: const LineHeight(1.5),
        padding: HtmlPaddings.zero,
        fontSize: FontSize(MySize.size14 ?? 14),
        margin: Margins.zero,
      ),
      "br": Style(
        height: Height(5), // Set height for <br> to 5 pixels
        margin: Margins.zero, // No margin
        lineHeight: const LineHeight(0.1),
      ),
    };
  }

  static String formatNumber(int number) {
    if (number >= 1000 && number < 1000000) {
      return "${(number / 1000).toStringAsFixed(1)}K";
    } else if (number >= 1000000 && number < 1000000000) {
      return "${(number / 1000000).toStringAsFixed(1)}M";
    } else if (number >= 1000000000) {
      return "${(number / 1000000000).toStringAsFixed(1)}B";
    } else {
      return number.toString();
    }
  }

  // Function to check password format and update conditions
  static void checkPasswordFormat(
      {required String passwordValue, required RxList passwordConditionsMet}) {
    String password = passwordValue;

    // Check for uppercase letter
    passwordConditionsMet[0] = RegExp(r'[A-Z]').hasMatch(password);

    // Check for minimum 8 characters
    passwordConditionsMet[1] = password.length >= 8;

    // Check for at least one number
    passwordConditionsMet[2] = RegExp(r'[0-9]').hasMatch(password);

    // Check for at least one special character
    passwordConditionsMet[3] =
        RegExp(r'[!@#$%^&*(),.?":{}|<>]').hasMatch(password);
  }

  static getNotificationOnTap(Map<String, dynamic> json) {
    // Directly access the 'action' and 'type' fields
    Map<String, dynamic> clickAction = jsonDecode(json["click_action"] ?? '{}');
    print("click action ==> $clickAction");
    String type = clickAction['type'] ?? '';

    int projectD = clickAction['ProjectId'] ?? 0;
    int postID = clickAction['PostId'] ?? 0;
    int parentProjectD = clickAction['ParentId'] ?? 0;

    switch (type) {
      case "team-invitation":
        return Get.toNamed(Routes.notifications);
      case "team-member-left":
        if (parentProjectD != 0) {
          return Get.toNamed(Routes.subProject_detail,
              arguments: {'projectId': projectD});
        } else {
          return Get.toNamed(Routes.project_detail,
              arguments: {'projectId': projectD});
        }
      case "community-joined":
        return Get.toNamed(Routes.community_detail,
            arguments: {'communityId': clickAction['CommunityId']});
      case "post-created":
        return Get.toNamed(Routes.notifications);
      case "user-followed":
        if (clickAction['UserId'] == CurrentUser.user.id) {
          return Get.toNamed(Routes.profile);
        } else {
          return Get.toNamed(Routes.other_user_profile,
              arguments: {'UserId': clickAction['UserId']});
        }
      case "team-invitation-response":
        return Get.toNamed(Routes.notifications);
        // return Get.toNamed(Routes.project_detail,arguments: {'projectId': projectD});
      case "Project-mentioned":
        return Get.toNamed(Routes.post_detail,
          arguments: {'postID': postID,"source": "Notification"});
        // return Get.toNamed(Routes.project_detail,arguments: {'projectId': projectD});

      case "Post Commented":
      case "Liked Post":
      case "Post-mentioned":
        return Get.toNamed(Routes.post_detail,arguments: {'postID': clickAction['PostId'],"source": "Notification"});

      case "Todo-DueDate":
        return Get.offAllNamed(Routes.Bottom_Bar,arguments: {"index": 4});
      default:
    }
  }

  static void showCustomSnackbar(
      {required String message,
      Duration duration = const Duration(milliseconds: 1500),
      Color? backgroundColor,
      bool isError = false,
      bool isDraft = false}) {
    Get.snackbar("", "",
        backgroundColor: backgroundColor ?? AppTheme.success,
        duration: duration,
        messageText: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              height: MySize.size32,
              width: MySize.size32,
              decoration: BoxDecoration(
                color: isError
                    ? AppTheme.white.withValues(alpha: 0.2)
                    : isDraft
                        ? AppTheme.white.withValues(alpha: 0.2)
                        : Color(0xFF40cb7c),
                shape: BoxShape.circle,
              ),
              padding: EdgeInsets.all(isError ? 7 : 5),
              child: SvgPicture.asset(
                isError ? "assets/svg/error.svg" : AppImage.checkIcon,
                color: AppTheme.white,
                height: MySize.size8,
                width: MySize.size12,
              ),
            ),
            Space.width(8),
            Expanded(
              child: TypoGraphy(
                text: message,
                level: 3,
                color: AppTheme.white,
              ),
            ),
          ],
        ),
        margin: EdgeInsets.only(top: MySize.size35 ?? 35),
        titleText: SizedBox.shrink(),
        colorText: AppTheme.white,
        maxWidth: MySize.size317,
        padding: EdgeInsets.only(
            left: MySize.size11 ?? 11,
            bottom: MySize.size10 ?? 15,
            top: MySize.size5 ?? 5));
  }

  static Future<void> getFcmToken() async {
    FirebaseMessaging messaging = FirebaseMessaging.instance;

    // Request permission and handle the result
    NotificationSettings settings = await messaging.requestPermission();

    // Check the authorization status
    if (settings.authorizationStatus == AuthorizationStatus.authorized) {
      try {
        // Get the FCM token
        String? fcmToken = await messaging.getToken();
        print("FCM Token: $fcmToken");

        if (fcmToken != null && fcmToken.isNotEmpty) {
          // Save the token using GetStorage or any other preferred storage
          // await box.write(StorageKey.tokenFcm, fcmToken);
          // CurrentUser.updateMe(params: {"fcmToken": fcmToken});
          // Initialize the FCM service
          await box.write("fcmToken", fcmToken);
          Get.put<FCMService>(FCMService()..init());

          if (box.read("finalToken") != null) {
            print('USER TOKEN === ${box.read("finalToken")}');
            final apiManager = ApiManager();
            await apiManager.callApi(
              APIS.user.updateMe,
              params: {
                'fcmToken': fcmToken,
              },
              successCallback: (response, message) {
                print("FCM response === $response");
              },
              failureCallback: (message, statusCode) {
                print("FCM error === $message");
              },
            );
          }
        } else {}
      } catch (e) {}
    } else if (settings.authorizationStatus == AuthorizationStatus.denied) {
      // Handle the case when permission is denied
      // showDialogDenied(
      //   context: context,
      //   message: "Please enable notifications to receive real-time updates.",
      //   title: "Notifications Disabled",
      // );
    } else if (settings.authorizationStatus ==
        AuthorizationStatus.notDetermined) {
      // Optionally, you could re-request the permission or provide additional instructions to the user
    } else if (settings.authorizationStatus ==
        AuthorizationStatus.provisional) {
      // Handle provisional permission, if needed
    }
  }

  static ApiModel prepareApi(ApiModel apiModel, Map<String, String> replace) {
    String newEndpoint = apiModel.endpoint;

    replace.forEach((key, value) {
      newEndpoint = newEndpoint.replaceAll(':$key', value);
    });

    return apiModel.copyWith(endpoint: newEndpoint);
  }

  Future<List<String>> getSuggestion(String input) async {
    List<String> placesList = [];
    placesList.clear();
    String apikey = GoogleMapConstants.apiKey;
    String baseUrl =
        "https://maps.googleapis.com/maps/api/place/autocomplete/json";
    String request = '$baseUrl?input=$input&key=$apikey';

    var response = await http.get(Uri.parse(request));
    var data = response.body.toString();

    if (response.statusCode == 200) {
      if (jsonDecode(data)['predictions'].length == 0) {
        placesList.clear();
      } else {
        jsonDecode(data)['predictions'].forEach((element) {
          placesList.add(
            element['description'],
          );
        });
      }

      return placesList;
    } else {
      throw Exception('Failed to load data!');
    }
  }

  static showDialogDenied(
      {required BuildContext context,
      String? message,
      String? title,
      VoidCallback? onCancel,
      VoidCallback? onPermissionChange}) {
    isPopupOpen.value = true;
    print("message open dialog ==> $message");
    return showDialog(
      barrierDismissible: false,
      context: context,
      useSafeArea: false,
      builder: (BuildContext context) {
        print("message open dialog ==> $message");
        return AlertDialog(
          title: TypoGraphy(text: title ?? 'Permission Required'),
          content: TypoGraphy(
              level: 3,
              text: message ??
                  'Some permissions are permanently denied. Please enable them in the settings.'),
          actions: <Widget>[
            TextButton(
              child: TypoGraphy(
                text: 'Cancel',
                color: AppTheme.red,
                level: 3,
              ),
              onPressed: () {
                if (onCancel != null) {
                  onCancel();
                } else {
                  isPopupOpen.value = false;
                  Navigator.of(context).pop();
                }
              },
            ),
            TextButton(
              child: TypoGraphy(
                text: 'Settings',
                color: AppTheme.primary1,
                level: 3,
              ),
              onPressed: () {
                openAppSettings();

                if (onPermissionChange != null) {
                  onPermissionChange();
                } else {
                  isPopupOpen.value = false;
                  Navigator.of(context).pop();
                }
              },
            ),
          ],
        );
      },
    );
  }

  static Future<void> requestPermissions({
    required List<Permission> permissions,
    required VoidCallback onPermissionsGranted,
    required BuildContext context,
    String? message,
    String? title,
  }) async {
    DeviceInfoPlugin deviceInfo = DeviceInfoPlugin();

    // Check Android version and assign appropriate permission
    if (Platform.isAndroid) {
      AndroidDeviceInfo androidInfo = await deviceInfo.androidInfo;
      int androidVersion = int.parse(androidInfo.version.release.split('.')[0]);
      if (androidVersion < 13) {
        permissions = [
          Permission.storage,
          ...permissions
        ]; // For Android 12 and below
      } else {
        permissions = [
          Permission.photos,
          ...permissions
        ]; // For Android 13 and above
      }
    }
    // Request permissions
    Map<Permission, PermissionStatus> statuses = await permissions.request();

    // Check if all permissions are granted
    bool allGranted = statuses.values
        .every((status) => (status.isGranted || status.isLimited));

    if (allGranted) {
      onPermissionsGranted();
    } else {
      // Check if any permission is permanently denied
      bool permanentlyDenied =
          statuses.values.any((status) => status.isPermanentlyDenied);

      if (permanentlyDenied) {
        print("message ==> $message");
        print("title ==> $title");
        // Show dialog to navigate to settings
        showDialogDenied(context: context, message: message, title: title);
      } else {
        // Re-request permissions
        await requestPermissions(
          permissions: permissions,
          onPermissionsGranted: onPermissionsGranted,
          context: context,
        );
      }
    }
  }

  static Future<File?> pickImageFromGallery({
    bool isAspectRatio = true,
    double? ratioX,
    double? ratioY,
    bool isNeedAspectRatio = true,
    bool hideBottomControl = false,
    bool isNeedCropping = true,
  }) async {
    final ImagePicker imagePicker = ImagePicker();
    final pickedFile = await imagePicker.pickImage(source: ImageSource.gallery);

    if (pickedFile == null) return null;

    if (!isNeedCropping) {
      return File(pickedFile.path);
    }

    CroppedFile? croppedFile = await ImageCropper().cropImage(
      aspectRatio: isNeedAspectRatio
          ? CropAspectRatio(
              ratioX: MySize.getScaledSizeWidth(ratioX ?? 430),
              ratioY: MySize.getScaledSizeHeight(ratioY ?? 183),
            )
          : null,
      sourcePath: pickedFile.path,
      uiSettings: [
        AndroidUiSettings(
          toolbarTitle: 'Edit Photo',
          toolbarColor: AppTheme.white,
          toolbarWidgetColor: AppTheme.primary1,
          initAspectRatio: CropAspectRatioPreset.original,
          lockAspectRatio: isAspectRatio,
          showCropGrid: false,
          hideBottomControls: hideBottomControl,
        ),
        IOSUiSettings(
          title: 'Edit Photo',
        ),
        WebUiSettings(
          context: Get.context!,
        ),
      ],
    );

    return croppedFile != null ? File(croppedFile.path) : null;
  }

  static logOut() async {
    final isDarkMode = box.read('isDarkMode');
    CurrentUser.clearMe();
    await CurrentUser.setFcm(fcm: "asd");
    box.erase();
    if (isDarkMode != null) {
      box.write('isDarkMode', isDarkMode);
    }
    final GoogleSignIn googleSignIn = GoogleSignIn();
    if (await googleSignIn.isSignedIn()) {
      await googleSignIn.disconnect(); // Disconnect the account
      await googleSignIn.signOut(); // Sign out from Google
    }

    await FirebaseAuth.instance.signOut();

    Get.offAllNamed(Routes.ONBOARDING);
  }

  static Future<File?> pickImageFromCamera({
    bool isAspectRatio = true,
    double? ratioX,
    double? ratioY,
    bool isNeedAspectRatio = true,
    bool hideBottomControl = true,
    bool isNeedCropping = true,
  }) async {
    final ImagePicker imagePicker = ImagePicker();
    try {
      final pickedFile =
          await imagePicker.pickImage(source: ImageSource.camera);

      if (pickedFile == null) return null;

      if (!isNeedCropping) {
        return File(pickedFile.path);
      }

      CroppedFile? croppedFile = await ImageCropper().cropImage(
        aspectRatio: isNeedAspectRatio
            ? CropAspectRatio(
                ratioX: MySize.getScaledSizeWidth(ratioX ?? 430),
                ratioY: MySize.getScaledSizeHeight(ratioY ?? 183),
              )
            : null,
        sourcePath: pickedFile.path,
        uiSettings: [
          AndroidUiSettings(
            toolbarTitle: 'Edit Photo',
            toolbarColor: Colors.white,
            toolbarWidgetColor: AppTheme.primary1,
            initAspectRatio: CropAspectRatioPreset.original,
            lockAspectRatio: isAspectRatio,
            showCropGrid: false,
            hideBottomControls: hideBottomControl,
          ),
          IOSUiSettings(
            title: 'Edit Photo',
          ),
          WebUiSettings(
            context: Get.context!,
          ),
        ],
      );
      return croppedFile != null ? File(croppedFile.path) : null;
    } catch (e) {
      print("error == ${e.toString()}");
    }
  }

  static Future<File?> pickVideo() async {
    final ImagePicker imagePicker = ImagePicker();
    final pickedFile = await imagePicker.pickVideo(source: ImageSource.gallery);
    if (pickedFile != null) {
      return File(pickedFile.path);
    }
    return null;
  }
}
