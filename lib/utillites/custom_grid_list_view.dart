import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:incenti_ai/utillites/typography.dart';
import '../app/routes/app_pages.dart';
import '../constants/app_image.dart';
import '../constants/app_size_constant.dart';
import '../models/app_project_model.dart';
import 'app_theme.dart';
import 'common_function.dart';
import 'current_user.dart';
import 'network_image.dart';

class CustomProjectGrid extends StatelessWidget {
  final List<Project> projects;
  final Function(int) onTap;
  final VoidCallback? callBack;
  final bool isUser;

  const CustomProjectGrid(
      {super.key,
      required this.projects,
      required this.onTap,
      this.callBack,
      this.isUser = false});

  @override
  Widget build(BuildContext context) {
    return SliverPadding(
      padding: EdgeInsets.zero,
      sliver: SliverGrid(
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 2,
          crossAxisSpacing: MySize.size20 ?? 20,
          mainAxisSpacing: MySize.size10 ?? 30,
          childAspectRatio: MediaQuery.of(context).size.width > 350 ? 0.82 : 0.70, //0.75
        ),
        delegate: SliverChildBuilderDelegate(
          (context, index) {
            var project = projects[index];
            return InkWell(
              onTap: () {
                if (!isUser && project.isPrivate) {
                  CommonFunction.showCustomSnackbar(
                    message: "This Project is Private.",
                  );
                } else {
                  Get.toNamed(Routes.project_detail,
                      arguments: {"projectId": project.id,"isNeedBottom": true})?.then((value) {
                    callBack?.call();
                  });
                }
              },
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Stack(
                    children: [
                      Container(
                        height: MySize.size142,
                        width: double.infinity,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(25),
                        ),
                        child: project.image.isNotEmpty
                            ? ClipRRect(
                                borderRadius: BorderRadius.circular(25),
                                child: NetworkImageComponent(
                                  imageUrl: project.image,
                                  simmerHeight: MySize.size142,
                                  width: double.infinity,
                                ),
                              )
                            : Image.asset(
                                AppImage.defaultImage,
                                width: double.infinity,
                                height: MySize.size142,
                              ),
                      ),
                      // if (project.isPrivate) ...[
                      //   Positioned(
                      //       right: MySize.getScaledSizeWidth(12.11),
                      //       bottom: MySize.getScaledSizeHeight(10),
                      //       child: Image.asset(
                      //         AppImage.lockProject,
                      //         height: MySize.getScaledSizeHeight(32),
                      //         width: MySize.getScaledSizeWidth(32),
                      //       )
                      //       ),
                      // ],
                      if (project.userId == CurrentUser.user.id || (project.projectMembers.isNotEmpty || !project.isPrivate) || isUser)
                      // if (project.userId == CurrentUser.user.id || !project.isPrivate)
                        Positioned(
                          right: MySize.getScaledSizeWidth(12.11),
                          top: MySize.getScaledSizeHeight(12),
                          child: InkWell(
                            onTap: () {
                              HapticFeedback.lightImpact();
                              onTap(index);
                            },
                            child: SvgPicture.asset(
                              AppImage.moreVertIcon,
                              color: AppTheme.white,
                              height: MySize.size24,
                              width: MySize.size24,
                            ),
                          ),
                        )
                    ],
                  ),
                  Space.height(6),
                  // TypoGraphy(
                  //   text: '${project.subProjectsCount} Sub-projects',
                  //   level: 2,
                  //   fontWeight: FontWeight.w400,
                  // ),
                  Space.height(4),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      if (project.isPrivate) ...[
                        Image.asset(
                          AppImage.lockProject,
                          height: MySize.getScaledSizeHeight(26),
                          width: MySize.getScaledSizeWidth(26),
                        ),
                        Space.width(8),
                      ],
                      Expanded(
                        child: TypoGraphy(
                          text: project.name,
                          textStyle: const TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.w700,
                            fontFamily: 'Inter',
                          ),
                          overflow: TextOverflow.ellipsis,
                          maxLines: 2,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            );
          },
          childCount: projects.length,
        ),
      ),
    );
  }
}
