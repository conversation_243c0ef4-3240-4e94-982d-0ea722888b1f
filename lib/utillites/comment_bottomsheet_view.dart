import 'dart:developer';
import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:intl/intl.dart';

import '../app/routes/app_pages.dart';
import '../constants/app_image.dart';
import '../constants/app_size_constant.dart';
import '../constants/constant.dart';
import '../main.dart';
import '../models/app_comment_model.dart';
import '../services/api_manager.dart';
import '../utillites/current_user.dart';
import '../utillites/typography.dart';
import '../utillites/search_field.dart';
import 'app_theme.dart';
import 'common_function.dart';
import 'common_profile_widget.dart';
import 'loader.dart';

class CommonBottomSheet extends StatefulWidget {
  final int index;
  final int userId;
  final bool isBookMark;
  final RxBool isLoading;
  final List<Comment> commentDataList;
  final TextEditingController commentController;
  final FocusNode commentFocusNode;
  final VoidCallback onCommentSend;

  const CommonBottomSheet({
    super.key,
    required this.index,
    this.isBookMark = false,
    required this.isLoading,
    required this.commentDataList,
    required this.commentController,
    required this.commentFocusNode,
    required this.onCommentSend,
    required this.userId,
  });

  @override
  State<CommonBottomSheet> createState() => _CommonBottomSheetState();
}

class _CommonBottomSheetState extends State<CommonBottomSheet> {
  OverlayEntry? _popupEntry;
  Offset _tapPosition = Offset.zero;
  int? _selectedCommentIndex; // Track which comment is selected

  void _showPopupBelowComment(GlobalKey key, int commentIndex) {
    setState(() {
      _selectedCommentIndex = commentIndex;
    });

    final RenderBox renderBox =
        key.currentContext!.findRenderObject() as RenderBox;
    final Offset position = renderBox.localToGlobal(Offset.zero);
    final Size size = renderBox.size;

    _popupEntry?.remove();
    _popupEntry = OverlayEntry(
      builder: (context) => Material(
        color: Colors.transparent,
        child: GestureDetector(
          behavior: HitTestBehavior.translucent,
          onTap: () => _removeOverlay(),
          child: Container(
            width: double.infinity,
            height: double.infinity,
            child: Stack(
              children: [
                // Background blur overlay
                BackdropFilter(
                  filter: ImageFilter.blur(sigmaX: 3, sigmaY: 3),
                  child: Container(
                    color: Colors.black.withOpacity(0.3),
                  ),
                ),
                // Selected comment (unblurred)
                Positioned(
                  left: 20,
                  right: 20,
                  top: position.dy - 10,
                  child: Container(
                    padding: const EdgeInsets.all(10),
                    decoration: BoxDecoration(
                      color: AppTheme.subBottom,
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                          color: AppTheme.borderColor.withOpacity(0.3)),
                    ),
                    child: _buildCommentItem(
                        widget.commentDataList[commentIndex], commentIndex,
                        isHighlighted: true),
                  ),
                ),
                // Popup menu
                Positioned(
                  left: 20,
                  right: 20,
                  top: position.dy + size.height + 18,
                  child: Material(
                    elevation: 8,
                    borderRadius: BorderRadius.circular(12),
                    color: AppTheme.borderColor,
                    child: Container(
                      decoration: BoxDecoration(
                        color: AppTheme.borderColor,
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                            color: AppTheme.borderColor.withOpacity(0.5)),
                      ),
                      padding: const EdgeInsets.symmetric(vertical: 8),
                      width: MySize.getScaledSizeWidth(100),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          _buildPopupItem("Delete", () {
                            _removeOverlay();
                            // widget.onCommentDelete();
                            callApiForDeleteComment(
                              context: context,
                              commentId: widget.commentDataList[commentIndex].id,
                              commentDataList: widget.commentDataList,
                            );
                            // deleteComment(commentId);
                          }),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );

    Overlay.of(context).insert(_popupEntry!);
  }

  void _removeOverlay() {
    _popupEntry?.remove();
    _popupEntry = null;
    setState(() {
      _selectedCommentIndex = null;
    });
  }

  Widget _buildPopupItem(String text, VoidCallback onTap) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8),
      child: Padding(
        padding: EdgeInsets.symmetric(
            horizontal: MySize.getScaledSizeWidth(16),
            vertical: MySize.getScaledSizeWidth(12)),
        child: Row(
          children: [
            SvgPicture.asset(
              AppImage.deleteTodo,
              width: MySize.getScaledSizeWidth(20),
              height: MySize.getScaledSizeHeight(20),
              color: AppTheme.red,
            ),
            Space.width(5),
            TypoGraphy(
              text: text,
              level: 3,
              fontWeight: FontWeight.w500,
              color: AppTheme.red,
            )
          ],
        ),
      ),
    );
  }

  Widget _buildCommentItem(Comment comment, int index,
      {bool isHighlighted = false}) {
    final GlobalKey itemKey = GlobalKey();

    return GestureDetector(
      key: itemKey,
      onTapDown: (details) {
        _tapPosition = details.globalPosition;
      },
      onLongPress: () {
        HapticFeedback.lightImpact();
        if (comment.user?.id == CurrentUser.user.id ||
            widget.userId == CurrentUser.user.id) {
          print("Long pressed on comment: ${comment.id}");
          _showPopupBelowComment(itemKey, index);
        }
      },
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        decoration: BoxDecoration(
          color: isHighlighted ? AppTheme.subBottom : Colors.transparent,
          borderRadius: BorderRadius.circular(8),
        ),
        child: InkWell(
          onTap: (){
            // Navigator.pop(context);
            if(comment.user?.id == CurrentUser.user.id) {
              Get.toNamed(Routes.profile);
            } else {
              Get.toNamed(Routes.other_user_profile,
                  arguments: {"UserId": comment.user?.id});
            }
          },
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              profileImage(
                url: comment.user?.image ?? "",
                userName: comment.user?.firstName ?? "",
                width: MySize.size38 ?? 38,
                height: MySize.size38 ?? 38,
                iconHeight: MySize.size38,
                iconWidth: MySize.size38,
                strokeWidth: MySize.size1 ?? 1,
                borderColor: Colors.transparent,
                color: AppTheme.darkGrey[100],
              ),
              Space.width(14),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    RichText(
                      text: TextSpan(
                        children: [
                          TextSpan(
                            text:
                                "${comment.user?.firstName} ${comment.user?.lastName} ",
                            style: GoogleFonts.inter(
                              fontSize: 16,
                              fontWeight: FontWeight.w700,
                              color: AppTheme.whiteWithBase,
                            ),
                          ),
                          TextSpan(
                            text: comment.comment,
                            style: GoogleFonts.inter(
                              fontSize: 16,
                              fontWeight: FontWeight.w400,
                              color: AppTheme.whiteWithBase,
                            ),
                          ),
                        ],
                      ),
                    ),
                    Space.height(8),
                    TypoGraphy(
                      text: DateFormat('MMM d, y').format(
                        DateTime.parse(comment.createdAt.toString()),
                      ),
                      level: 2,
                      fontWeight: FontWeight.w400,
                      color: AppTheme.grey,
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: _removeOverlay,
      child: BackdropFilter(
        filter: ImageFilter.blur(sigmaX: 5, sigmaY: 5),
        child: FractionallySizedBox(
          heightFactor: 0.85,
          child: Container(
            decoration: BoxDecoration(
              color: AppTheme.subBottom,
              border: BorderDirectional(
                top: BorderSide(color: AppTheme.borderColor),
                start: BorderSide(color: AppTheme.borderColor),
                end: BorderSide(color: AppTheme.borderColor),
              ),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(40),
                topRight: Radius.circular(40),
              ),
            ),
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20),
              child: Column(
                children: [
                  Space.height(8),
                  Container(
                    height: MySize.size5,
                    width: MySize.size34,
                    decoration: BoxDecoration(
                      color: box.read('isDarkMode') ? AppTheme.grey : AppTheme.black.withOpacity(0.2),
                      borderRadius: BorderRadius.circular(100),
                    ),
                  ),
                  Space.height(20),
                  Align(
                    alignment: Alignment.centerLeft,
                    child: TypoGraphy(
                      text: "Comments",
                      level: 5,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  Space.height(20),
                  Expanded(
                    child: Obx(
                      () => widget.isLoading.value
                          ? Loader()
                          : widget.commentDataList.isNotEmpty
                              ? ListView.separated(
                                  separatorBuilder: (_, __) => Space.height(30),
                                  itemCount: widget.commentDataList.length,
                                  itemBuilder: (context, idx) {
                                    final comment =
                                        widget.commentDataList[idx];

                                    // Apply blur effect to non-selected comments when popup is open
                                    Widget commentWidget =
                                        _buildCommentItem(comment, idx);

                                    if (_selectedCommentIndex != null &&
                                        _selectedCommentIndex != idx) {
                                      commentWidget = commentWidget;
                                    }

                                    return commentWidget;
                                  },
                                )
                              : Center(
                                  child: TypoGraphy(
                                      text: "No comments yet", level: 5),
                                ),
                    ),
                  ),
                  const SizedBox(height: 20),
                  Padding(
                    padding: EdgeInsets.only(
                      bottom: MediaQuery.of(context).viewInsets.bottom + 25,
                    ),
                    child: ValueListenableBuilder<TextEditingValue>(
                      valueListenable: widget.commentController,
                      builder: (context, value, child) {
                        Widget textField = SearchAppTextField(
                          textCapitalization: TextCapitalization.sentences,
                          controller: widget.commentController,
                          focusNode: widget.commentFocusNode,
                          hintText: "Write a comment",
                          hintStyle: TextStyle(
                            fontSize: 16,
                            fontFamily: "Inter",
                            fontWeight: FontWeight.w400,
                            color: AppTheme.grey,
                          ),
                          maxLines: null,
                          suffixIcon: value.text.trim().isNotEmpty
                              ? InkWell(
                                  onTap: widget.onCommentSend,
                                  child: Padding(
                                    padding: const EdgeInsets.only(right: 23),
                                    child:
                                        SvgPicture.asset(AppImage.commentSend),
                                  ),
                                )
                              : const SizedBox(),
                        );

                        // Blur the text field when popup is open
                        if (_selectedCommentIndex != null) {
                          textField = ImageFiltered(
                            imageFilter: ImageFilter.blur(sigmaX: 2, sigmaY: 2),
                            child: Opacity(
                              opacity: 0.3,
                              child: textField,
                            ),
                          );
                        }

                        return textField;
                      },
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}

ApiManager apiManager = ApiManager();

Future<void> callApiForDeleteComment(
    {required BuildContext context,
      required int? commentId,required List<Comment> commentDataList}) {
  FocusScope.of(context).unfocus();
  final ApiModel deletePost =
  ApiModel("/post-comments/$commentId", APIType.DELETE);
  // isLoading.value = false;
  return apiManager.callApi(
    deletePost,
    successCallback: (response, message) async {
      try {
        if (response['status'] == 'success') {
          log("response === $response");
          // isLoading.value = false;
          commentDataList.removeWhere((element) => element.id == commentId);
          CommonFunction.showCustomSnackbar(
            message: response['message'],
          );
        }
      } catch (error) {
        // isLoading.value = true;
      }
    },
    failureCallback: (message, statusCode) {
      // isLoading.value = true;
    },
  );
}
