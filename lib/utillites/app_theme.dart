import 'package:flutter/material.dart';
import 'package:get_storage/get_storage.dart';
import 'package:incenti_ai/utillites/typography.dart';
import '../constants/app_size_constant.dart';
import '../main.dart';
import '../models/theme_model/font_size.dart';

double paddingHoriZontal = MySize.size15 ?? 15;

class AppTheme {
  // ------------------------ Theme Color

  static const Color primary1 = Color(0xFF6D11D2);
  static const Color subPrimary = Color(0xFFF6EFFE);
  static const Color baseBlack = Color(0xFF2D394A);
  static const Color borderColor = Color(0xFF3C4658);
  static const Color mutedTextColor = Color(0xFF787E89);

  static MaterialColor grey =
  createColorMap(getColorInt("#787E89"), colorsShads: [
    getColorInt("#d9d9d9"),
  ]);

  static MaterialColor yellow =
  createColorMap(getColorInt("#E8AB2E"), colorsShads: [
    getColorInt("#fff9db"),
    getColorInt("#fff4b6"),
    getColorInt("#ffee92"),
    getColorInt("#ffe86d"),
    getColorInt("#ffe249"),
    getColorInt("#ffdd24"),
    getColorInt("#d9b700"),
    getColorInt("#b39600"),
    getColorInt("#8c7600"),
    getColorInt("#665600")
  ]);

  static MaterialColor red =
  createColorMap(getColorInt("#EF3B41"), colorsShads: [
    getColorInt("#f9dde0"),
    getColorInt("#f3bcc1"),
    getColorInt("#ee9aa2"),
    getColorInt("#e87883"),
    getColorInt("#e25764"),
    getColorInt("#cb2333"),
    getColorInt("#ac1e2c"),
    getColorInt("#8e1924"),
    getColorInt("#70131c"),
    getColorInt("#520e15")
  ]);

  static MaterialColor white =
  createColorMap(getColorInt("#FFFFFF"), colorsShads: [
    getColorInt("#ffffff"),
    getColorInt("#d7d7d7"),
    getColorInt("#aeaeae"),
    getColorInt("#868686"),
    getColorInt("#5e5e5e"),
  ]);

  static MaterialColor darkGrey =
  createColorMap(getColorInt("#343A40"), colorsShads: [
    getColorInt("#d4d8dc"),
    getColorInt("#a8b0b8"),
    getColorInt("#7d8995"),
    getColorInt("#57616b"),
    getColorInt("#262b2f"),
  ]);

  static MaterialColor black =
  createColorMap(getColorInt("#000000"), colorsShads: [
    getColorInt("#dbdbdb"),
    getColorInt("#b6b6b6"),
    getColorInt("#929292"),
    getColorInt("#6d6d6d"),
    getColorInt("#494949"),
    getColorInt("#242424"),
  ]);

  static const Color success = Color(0xFF10BE5B);
  static const Color lightGrey = Color(0xFFF1F2F3);
  static const Color lightGrey1 = Color(0xFF2F3136);
  static const Color warning = Color(0xFFFF8F00);
  static const Color danger = Color(0xFFD32F2F);
  static const Color disable = Color.fromARGB(255, 204, 204, 204);
  static const Color processing = Color(0xFF448AFF);
  static const Color rework = Color(0xFF722ED1);

  static LinearGradient primaryGradient = LinearGradient(
    colors: [primary1, primary1],
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
  );

  // ------------------------ Text Color
  static const Color textPrimary = Color(0xFF000000);
  static const Color textBackGroundPrimary = Color.fromRGBO(0, 0, 0, 0.0);
  static const Color textHighlight = Color(0xFFFF8F00);
  static const Color textHighlightBackground = Color(0xFFD32F2F);
  static const Color buttonDisableColor = Color(0xFFdbecff);
  static const Color buttonTextDisableColor = Color(0xFF007BFF);
  static const Color whiteF4F4F4 = Color(0xffF4F4F4);

  // ------------------------ Font Size
  static Font fontSize = Font(
    f1: FontLevel(fontSize: MySize.size18 ?? 10, fontWeight: FontWeight.w500),
    f2: FontLevel(fontSize: MySize.size12 ?? 12, fontWeight: FontWeight.w500),
    f3: FontLevel(fontSize: MySize.size14 ?? 14, fontWeight: FontWeight.w500),
    f4: FontLevel(fontSize: MySize.size16 ?? 16, fontWeight: FontWeight.w600),
    f5: FontLevel(fontSize: MySize.size20 ?? 20, fontWeight: FontWeight.w500),
    f6: FontLevel(fontSize: MySize.size24 ?? 24, fontWeight: FontWeight.w700),
    f7: FontLevel(fontSize: MySize.size28 ?? 28, fontWeight: FontWeight.w700),
    f8: FontLevel(fontSize: MySize.size30 ?? 30, fontWeight: FontWeight.w600),
    f9: FontLevel(fontSize: MySize.size36 ?? 36, fontWeight: FontWeight.w700),
    f10: FontLevel(fontSize: MySize.size38 ?? 38, fontWeight: FontWeight.w800),
    f11: FontLevel(fontSize: MySize.size22 ?? 22, fontWeight: FontWeight.w700),
    f12: FontLevel(fontSize: MySize.size18 ?? 18, fontWeight: FontWeight.w700),
  );

  // ------------------------ Tab View
  static double borderRadius = 10;

  static Decoration activeTabDecoration = BoxDecoration(
    gradient: primaryGradient,
    border: Border.all(color: primary1),
    borderRadius: BorderRadius.circular(borderRadius),
  );

  static TextStyle activeTextStyle = myTextStyle(
    fontLevel: getLevelFromNumber(6),
    color: white,
  );

  // ------------------------ Buttons
  static const double buttonHight = 70;
  static const int buttonTextLevel = 5;
  static const FontWeight buttonTextWeight = FontWeight.w500;
  static double borderWidth = MySize.size1 ?? 2;
  static BorderRadius buttonBorderRadius = BorderRadius.circular(20);

  // ------------------------ Dark Theme
  static const Color darkBackground = Color(0xFF121722);
  static const Color bottom = Color(0xFF121622);
  static const Color bottomBar = Color(0xFF262A34);
  static const Color darkSurface = Color(0xFF1E1E1E);
  static const Color darkPrimary = Color(0xFF9D4EDD);
  static const Color darkTextPrimary = Color(0xFFFFFFFF);
  static const Color darkTextSecondary = Color(0xFFB3B3B3);
  static const Color darkDivider = Color(0xFF2C2C2C);
  static const Color darkError = Color(0xFFCF6679);

  // ------------------------ Light Theme
  static const Color lightBackground = Color(0xFFFFFFFF);
  static const Color lightSurface = Color(0xFFF5F5F5);
  static const Color lightText = Color(0xFF000000);

  // ------------------------ Dynamic Colors (use getters)
  static Color get primaryIconDark =>
      box.read('isDarkMode') == true ? AppTheme.white : AppTheme.primary1;

  static Color? get whiteWithNull =>
      box.read('isDarkMode') == true ? AppTheme.white : null;

  static Color get whiteWithBase =>
      box.read('isDarkMode') == true ? AppTheme.white : AppTheme.baseBlack;

  static Color get appTextField =>
      box.read('isDarkMode') == true ? bottomBar : const Color(0xFFF1F2F3);

  static Color get appBottomSheet =>
      box.read('isDarkMode') == true ? bottomBar : AppTheme.white;

  static Color get borderWithTrans =>
      box.read('isDarkMode') == true ? AppTheme.borderColor : Colors.transparent;

  static Color get baseShimmer =>
      box.read('isDarkMode') == true ? Colors.grey[700]! : Colors.grey[300]!;

  static Color get highlightShimmer =>
      box.read('isDarkMode') == true ? Colors.grey[500]! : Colors.grey[100]!;

  static Color get subBottom =>
      box.read('isDarkMode') == true ? const Color(0xFF181A20) : AppTheme.white;

  // ------------------------ Context-aware Colors
  static Color getBottomBackgroundColor(BuildContext context) {
    return Theme.of(context).brightness == Brightness.dark ? bottom : white;
  }

  static Color getTextPrimaryColor(BuildContext context) {
    return Theme.of(context).brightness == Brightness.dark
        ? darkTextPrimary
        : baseBlack;
  }

  static Color getBottomNavigationBarColor(BuildContext context) {
    return Theme.of(context).brightness == Brightness.dark
        ? darkBackground
        : primary1;
  }

  static Color getIconColor(BuildContext context) {
    return Theme.of(context).brightness == Brightness.dark
        ? darkTextPrimary
        : lightText;
  }

  static Color? getSubTextColor(BuildContext context) {
    return Theme.of(context).brightness == Brightness.dark ? grey[50] : grey;
  }

  static Color getContrastTextColor(BuildContext context, Color backgroundColor) {
    final brightness = Theme.of(context).brightness;
    final luminance = backgroundColor.computeLuminance();
    return luminance > 0.5
        ? (brightness == Brightness.dark ? darkTextPrimary : lightText)
        : Colors.white;
  }
}

// ------------------------ Helpers
List<int> shads = [
  50,
  100,
  200,
  400,
  500,
  700,
  800,
  900,
  1000,
  1100,
  1200,
  1300,
  140,
  1500
];

int getColorInt(String color) {
  return int.parse('0xFF${color.replaceFirst('#', '')}');
}

MaterialColor createColorMap(int color, {List<int> colorsShads = const []}) {
  Map<int, Color> colorShads = {};

  for (int i = 0; i < colorsShads.length; i++) {
    colorShads[shads[i]] = Color(colorsShads[i]);
  }
  return MaterialColor(
    color,
    colorShads,
  );
}
