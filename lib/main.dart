import 'package:ffmpeg_kit_flutter_new/ffmpeg_kit_config.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_quill/flutter_quill.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:incenti_ai/utillites/app_module.dart';
import 'package:incenti_ai/utillites/app_theme.dart';
import 'package:incenti_ai/utillites/keyboard_dismiss_Observer.dart';
import 'package:incenti_ai/utillites/theme_controller.dart';
import 'package:incenti_ai/utillites/translation_service.dart';
import 'package:kiwi/kiwi.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'app/modules/story_editor/controllers/story_editor_controller.dart';
import 'app/routes/app_pages.dart';
import 'constants/config.dart';
import 'constants/constant.dart';

late KiwiContainer app;
GetStorage box = GetStorage();

Future<void> main() async {
  CONFIG().config(Environment.staging);
  WidgetsFlutterBinding.ensureInitialized();
  await FFmpegKitConfig.init();
  await StoryBackgroundUploadService.initialize();
  await GetStorage.init();
  setup();
  app = KiwiContainer();
  await Firebase.initializeApp();
  // SystemChrome.setSystemUIOverlayStyle(SystemUiOverlayStyle(
  //   statusBarIconBrightness: Brightness.light,
  //   statusBarBrightness: Brightness.dark,
  // ));
  SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
  ]).then((_) {
    box.remove("chat_messages");
    box.write("userMessageCount", 0);
    if(box.read('isDarkMode') == null) {
      box.write("isDarkMode", true);
    }
    runApp(
      Obx(() {
        final localizationController = Get.put(TranslationService());
        final themeController = Get.put(ThemeController());
        return GetMaterialApp(
          navigatorObservers: [
            KeyboardDismissObserver(),
          ],
          locale: localizationController.locale,
          debugShowCheckedModeBanner: false,
          title: "Floment",
          initialRoute: AppPages.INITIAL,
          getPages: AppPages.routes,
          supportedLocales: const [
            Locale('en', 'US'), // English
            Locale('hi', 'IN'), // Hindi
          ],
          localizationsDelegates: const [
            FlutterQuillLocalizations.delegate,
            GlobalMaterialLocalizations.delegate,
            GlobalWidgetsLocalizations.delegate,
            GlobalCupertinoLocalizations.delegate,
          ],
          builder: (context, child) {
            final mediaQueryData = MediaQuery.of(context);
            final scale = mediaQueryData.textScaleFactor.clamp(1.0, 1.0);
            return MediaQuery(
              data:
              mediaQueryData.copyWith(textScaler: TextScaler.linear(scale)),
              child: child!,
            );
          },
          theme: ThemeData(
            useMaterial3: false,
            scaffoldBackgroundColor: AppTheme.lightBackground,
            primaryColor: AppTheme.primary1,
            textTheme: GoogleFonts.interTextTheme().apply(
              bodyColor: AppTheme.lightText,
              displayColor: AppTheme.lightText,
            ),
            splashColor: Colors.transparent,
            highlightColor: Colors.transparent,
            colorScheme: ColorScheme.light(
              primary: AppTheme.primary1,
              secondary: AppTheme.primary1,
              surface: AppTheme.lightSurface,
              background: AppTheme.lightBackground,
              error: AppTheme.danger,
              onPrimary: Colors.white,
              onSecondary: Colors.white,
              onSurface: AppTheme.lightText,
              onBackground: AppTheme.lightText,
              onError: Colors.white,
            ),
          ),
          darkTheme: ThemeData(
            useMaterial3: false,
            scaffoldBackgroundColor: AppTheme.darkBackground,
            primaryColor: AppTheme.darkPrimary,
            textTheme: GoogleFonts.interTextTheme(ThemeData.dark().textTheme).apply(
              bodyColor: AppTheme.darkTextPrimary,
              displayColor: AppTheme.darkTextPrimary,
            ),
            splashColor: Colors.transparent,
            highlightColor: Colors.transparent,
            cardColor: AppTheme.darkSurface,
            dividerColor: AppTheme.darkDivider,
            colorScheme: const ColorScheme.dark(
              primary: AppTheme.darkPrimary,
              secondary: AppTheme.darkPrimary,
              surface: AppTheme.darkSurface,
              background: AppTheme.darkBackground,
              error: AppTheme.darkError,
              onPrimary: Colors.white,
              onSecondary: Colors.white,
              onSurface: AppTheme.darkTextPrimary,
              onBackground: AppTheme.darkTextPrimary,
              onError: Colors.white,
            ),
          ),
          themeMode: themeController.theme,
        );
      }),
    );
  });
}
