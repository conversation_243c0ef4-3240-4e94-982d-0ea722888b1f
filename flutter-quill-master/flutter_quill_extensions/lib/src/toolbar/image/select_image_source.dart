import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter_quill/internal.dart';
import 'package:get_storage/get_storage.dart';

import '../../editor/image/image_embed_types.dart';

class SelectImageSourceDialog extends StatelessWidget {
  const SelectImageSourceDialog({super.key});

  @override
  Widget build(BuildContext context) {
    final box = GetStorage();
    return Container(
      constraints: const BoxConstraints(minHeight: 200),
      width: double.infinity,
      decoration: BoxDecoration(
        color: box.read('isDarkMode') ? const Color(0xFF262A34) : Colors.white,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(40),
          topRight: Radius.circular(40),
        ),
      ),
      child: SingleChildScrollView(
        child: <PERSON>umn(
          children: [
            const SizedBox(
              height: 8,
            ),
            Container(
              height: 5,
              width: 34,
              decoration: BoxDecoration(
                color: box.read('isDarkMode') ? const Color(0xFF787E89) : Colors.black.withOpacity(0.2),
                borderRadius: BorderRadius.circular(100),
              ),
            ),
            const SizedBox(
              height: 20,
            ),
            ListTile(
              title: Text(context.loc.gallery),
              subtitle: Text(
                context.loc.pickAPhotoFromYourGallery,
              ),
              leading: const Icon(Icons.photo_sharp),
              onTap: () => Navigator.of(context).pop(InsertImageSource.gallery),
            ),
            ListTile(
              title: Text(context.loc.camera),
              subtitle: Text(
                context.loc.takeAPhotoUsingYourCamera,
              ),
              leading: const Icon(Icons.camera),
              enabled: !isDesktopApp,
              onTap: () => Navigator.of(context).pop(InsertImageSource.camera),
            ),
            ListTile(
              title: Text(context.loc.link),
              subtitle: Text(
                context.loc.pasteAPhotoUsingALink,
              ),
              leading: const Icon(Icons.link),
              onTap: () => Navigator.of(context).pop(InsertImageSource.link),
            ),
            const SizedBox(
              height: 20,
            ),
          ],
        ),
      ),
    );
  }
}

Future<InsertImageSource?> showSelectImageSourceDialog({
  required BuildContext context,
}) async {
  final imageSource = await showModalBottomSheet<InsertImageSource>(
    // showDragHandle: true,
    backgroundColor: Colors.transparent,
    context: context,
    constraints: const BoxConstraints(maxWidth: 640),
    builder: (_) => BackdropFilter(
      filter: ImageFilter.blur(sigmaX: 5, sigmaY: 5),
      child: const SelectImageSourceDialog(),
    ),
  );
  return imageSource;
}
